* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Google Sans', 'Roboto', 'Arial', sans-serif;
    line-height: 1.6;
    color: #202124;
    background-color: #f8f9fa;
    height: 100vh;
    overflow: hidden;
}

/* 頂部導航欄 */
.top-header {
    background: white;
    border-bottom: 1px solid #e8eaed;
    padding: 0 24px;
    height: 64px;
    display: flex;
    align-items: center;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo .material-icons {
    color: #1a73e8;
    font-size: 28px;
}

.logo h1 {
    color: #202124;
    font-size: 20px;
    font-weight: 500;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.icon-button {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    color: #5f6368;
    transition: background-color 0.2s;
}

.icon-button:hover {
    background-color: #f1f3f4;
}

/* 主要容器 */
.main-container {
    display: flex;
    height: calc(100vh - 64px);
    margin-top: 64px;
}

/* 左側面板 - 資料管理 */
.left-panel {
    width: 320px;
    background: white;
    border-right: 1px solid #e8eaed;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.panel-header {
    padding: 24px 24px 16px;
    border-bottom: none;
}

.panel-header h2 {
    font-size: 22px;
    font-weight: 400;
    color: #202124;
    margin-bottom: 16px;
}

.add-source-btn {
    background: white;
    color: #5f6368;
    border: 1px solid #dadce0;
    padding: 12px 24px;
    border-radius: 24px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.2s;
    width: 100%;
    font-weight: 500;
}

.add-source-btn:hover {
    background: #f8f9fa;
    border-color: #c4c7c5;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sources-list {
    flex: 1;
    overflow-y: auto;
    position: relative;
}

.sources-section {
    padding: 0 24px;
}

.sources-title {
    font-size: 14px;
    font-weight: 500;
    color: #5f6368;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e8eaed;
}

.refresh-btn {
    position: absolute;
    top: 16px;
    right: 16px;
    background: none;
    border: none;
    color: #5f6368;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
    z-index: 10;
}

.refresh-btn:hover {
    background: #f1f3f4;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #5f6368;
}

.empty-state .material-icons {
    font-size: 48px;
    margin-bottom: 16px;
    color: #dadce0;
}

.empty-state p {
    margin-bottom: 8px;
}

.empty-state .hint {
    font-size: 14px;
    color: #9aa0a6;
}

/* 文件上傳區域 */
.upload-section {
    padding: 16px;
    border-bottom: 1px solid #e8eaed;
}

.upload-zone {
    border: 2px dashed #dadce0;
    border-radius: 8px;
    padding: 24px 16px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
    margin-bottom: 16px;
}

.upload-zone:hover {
    border-color: #1a73e8;
    background: #f8f9ff;
}

.upload-zone.dragover {
    border-color: #1a73e8;
    background: #e8f0fe;
}

.upload-zone .material-icons {
    font-size: 36px;
    color: #5f6368;
    margin-bottom: 12px;
    display: block;
}

.upload-zone p {
    margin-bottom: 6px;
    color: #202124;
    font-size: 14px;
}

.upload-hint {
    font-size: 12px;
    color: #5f6368;
}

.upload-actions {
    display: flex;
    gap: 8px;
}

.upload-btn {
    flex: 1;
    background: #1a73e8;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: background-color 0.2s;
}

.upload-btn:hover:not(:disabled) {
    background: #1765cc;
}

.upload-btn:disabled {
    background: #dadce0;
    cursor: not-allowed;
}

.cancel-btn {
    background: #f1f3f4;
    color: #5f6368;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: background-color 0.2s;
}

.cancel-btn:hover {
    background: #e8eaed;
}

/* 上傳載入動畫 */
.upload-loading {
    padding: 24px;
    text-align: center;
    border-bottom: 1px solid #e8eaed;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #1a73e8;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 12px;
}

.upload-loading p {
    font-size: 14px;
    color: #5f6368;
}

/* 上傳結果 */
.upload-result {
    padding: 16px;
    border-bottom: 1px solid #e8eaed;
    font-size: 14px;
}

.upload-result.success {
    background: #e8f5e8;
    color: #2e7d32;
    border-left: 4px solid #4caf50;
}

.upload-result.error {
    background: #ffebee;
    color: #c62828;
    border-left: 4px solid #f44336;
}

.upload-result.info {
    background: #e3f2fd;
    color: #1565c0;
    border-left: 4px solid #2196f3;
}

.processing-stats {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 4px;
    margin-top: 8px;
    font-size: 12px;
}

.processing-stats h4 {
    margin: 0 0 8px 0;
    font-size: 13px;
    color: #495057;
}

.processing-stats ul {
    margin: 0;
    padding-left: 16px;
    list-style-type: disc;
}

.processing-stats li {
    margin-bottom: 4px;
}

/* 文件列表 */
.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 16px 8px;
    border-bottom: 1px solid #e8eaed;
}

.list-header h3 {
    font-size: 14px;
    font-weight: 500;
    color: #202124;
    margin: 0;
}

.refresh-btn {
    background: none;
    border: none;
    padding: 6px;
    border-radius: 50%;
    cursor: pointer;
    color: #5f6368;
    transition: background-color 0.2s;
}

.refresh-btn:hover {
    background: #f1f3f4;
}

.file-list-content {
    flex: 1;
    overflow-y: auto;
    padding: 8px;
}

/* 文件項目 - 簡潔列表風格 */
.file-item-compact {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.2s;
    gap: 12px;
}

.file-item-compact:hover {
    background: #f8f9fa;
}

.file-item-compact:last-child {
    border-bottom: none;
}

.file-item-compact.uploading {
    background: #f8f9fa;
    border: 1px solid #e8eaed;
    border-radius: 8px;
    margin-bottom: 8px;
}

.file-item-compact.uploading .file-name {
    color: #5f6368;
    font-style: italic;
}

.file-item-compact {
    cursor: pointer;
}

.file-item-compact:hover {
    background: #f8f9fa;
}

.file-item-compact .file-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
}

.file-item-compact .file-info {
    flex: 1;
    min-width: 0;
}

.file-item-compact .file-name {
    font-weight: 500;
    color: #202124;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 2px;
}

.file-item-compact .file-meta {
    font-size: 12px;
    color: #5f6368;
}

.file-status {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
}

.file-status.processed {
    color: #34a853;
}

.file-status.processing {
    color: #fbbc04;
    animation: pulse 2s infinite;
}

.file-status.uploading {
    color: #1a73e8;
}

.file-status.success-animation {
    color: #34a853;
    animation: successPop 0.5s ease-out;
}

/* 旋轉動畫 */
.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e8eaed;
    border-top: 2px solid #1a73e8;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes successPop {
    0% {
        transform: scale(0.5);
        opacity: 0;
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.delete-btn-compact {
    background: none;
    border: none;
    color: #5f6368;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: all 0.2s;
    opacity: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-item-compact:hover .delete-btn-compact {
    opacity: 1;
}

.delete-btn-compact:hover {
    background: #fce8e6;
    color: #d93025;
}

.delete-btn-compact .material-icons {
    font-size: 16px;
}



/* 資料庫統計樣式 */
.db-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.stat-item {
    background: #f1f3f4;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    color: #5f6368;
}

.db-stats.error {
    color: #d93025;
    font-size: 11px;
}

/* 文件詳細統計樣式 */
.file-detail-stats {
    margin-top: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #34a853;
}

.file-detail-stats h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    color: #202124;
    display: flex;
    align-items: center;
    gap: 8px;
}

.detail-section {
    margin-bottom: 16px;
}

.detail-section:last-child {
    margin-bottom: 0;
}

.detail-section h5 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #5f6368;
    font-weight: 500;
}

.detail-item {
    margin-bottom: 6px;
    font-size: 13px;
    line-height: 1.4;
}

.detail-item strong {
    color: #202124;
}

.detail-section.error .detail-item {
    color: #d93025;
    background: #fce8e6;
    padding: 8px 12px;
    border-radius: 6px;
    margin-bottom: 0;
}

/* 文件詳細視窗樣式 */
.file-detail-modal {
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.file-detail-section {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f1f3f4;
}

.file-detail-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.file-detail-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
}

.file-icon-large {
    font-size: 48px;
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 12px;
}

.file-basic-info h4 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 500;
    color: #202124;
}

.file-path {
    margin: 0;
    font-size: 12px;
    color: #5f6368;
    font-family: monospace;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
}

.file-detail-section h5 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 500;
    color: #202124;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
}

.detail-label {
    font-size: 12px;
    color: #5f6368;
    font-weight: 500;
}

.detail-value {
    font-size: 12px;
    color: #202124;
    font-weight: 500;
}

.error-message {
    color: #d93025;
    font-size: 14px;
    margin: 0;
    padding: 12px;
    background: #fce8e6;
    border-radius: 6px;
}

.file-detail-actions {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #f1f3f4;
    display: flex;
    justify-content: flex-end;
}

.delete-btn-detail {
    background: #d93025;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: background-color 0.2s;
}

.delete-btn-detail:hover {
    background: #b52d20;
}

.delete-btn-detail .material-icons {
    font-size: 16px;
}

.db-stats {
    margin-top: 6px;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
}

.db-stat {
    background: #e9ecef;
    color: #495057;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 500;
}

.db-stat.warning {
    background: #fff3cd;
    color: #856404;
}

.db-stat.error {
    background: #f8d7da;
    color: #721c24;
}

/* 刪除統計 */
.delete-stats {
    background: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
    margin-top: 8px;
    font-size: 11px;
}

.delete-stats h4 {
    margin: 0 0 6px 0;
    font-size: 12px;
    color: #495057;
}

.delete-stats ul {
    margin: 0;
    padding-left: 12px;
    list-style-type: disc;
}

.delete-stats li {
    margin-bottom: 2px;
}

/* 中間對話區域 */
.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: white;
    overflow: hidden;
}

.chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
}

.welcome-message {
    text-align: center;
    padding: 60px 40px;
    max-width: 600px;
    margin: 0 auto;
}

.welcome-icon {
    width: 80px;
    height: 80px;
    background: #e8f0fe;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
}

.welcome-icon .material-icons {
    font-size: 40px;
    color: #1a73e8;
}

.welcome-message h3 {
    font-size: 24px;
    font-weight: 500;
    color: #202124;
    margin-bottom: 16px;
}

.welcome-message p {
    color: #5f6368;
    font-size: 16px;
    line-height: 1.6;
}

/* 對話訊息 */
.message {
    margin-bottom: 24px;
    display: flex;
    gap: 12px;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: #1a73e8;
    color: white;
}

.message.assistant .message-avatar {
    background: #f1f3f4;
    color: #5f6368;
}

.message-content {
    max-width: 70%;
    width: fit-content;
    min-width: 60px;
    word-wrap: break-word;
    word-break: break-word;
}

.message.user .message-content {
    background: #1a73e8;
    color: white;
    padding: 12px 16px;
    border-radius: 18px 18px 4px 18px;
}

.message.assistant .message-content {
    background: #f8f9fa;
    color: #202124;
    padding: 12px 16px;
    border-radius: 18px 18px 18px 4px;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
}

/* 助手訊息內的文字格式 */
.message.assistant .message-content p {
    margin: 0 0 12px 0;
}

.message.assistant .message-content p:last-child {
    margin-bottom: 0;
}

.message.assistant .message-content ul,
.message.assistant .message-content ol {
    margin: 8px 0;
    padding-left: 20px;
}

.message.assistant .message-content li {
    margin: 4px 0;
}

.message.assistant .message-content h1,
.message.assistant .message-content h2,
.message.assistant .message-content h3,
.message.assistant .message-content h4 {
    margin: 16px 0 8px 0;
    font-weight: 600;
}

.message.assistant .message-content h1:first-child,
.message.assistant .message-content h2:first-child,
.message.assistant .message-content h3:first-child,
.message.assistant .message-content h4:first-child {
    margin-top: 0;
}

.message.assistant .message-content code {
    background: #e8eaed;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.message.assistant .message-content pre {
    background: #e8eaed;
    padding: 12px;
    border-radius: 6px;
    overflow-x: auto;
    margin: 8px 0;
}

.message.assistant .message-content blockquote {
    border-left: 4px solid #1a73e8;
    padding-left: 12px;
    margin: 8px 0;
    color: #5f6368;
}

.message.system {
    align-self: center;
    max-width: 90%;
    margin: 8px 0;
}

.message.system .message-content {
    background: #e8f0fe;
    color: #1a73e8;
    border: 1px solid #dadce0;
    text-align: left;
    padding: 12px 16px;
    border-radius: 8px;
}

.message.file-info .message-content {
    background: #f8f9fa;
    color: #202124;
    border-left: 4px solid #34a853;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.5;
}

.message-text {
    line-height: 1.5;
}

.message-sources {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #e8eaed;
}

.message-sources h4 {
    font-size: 12px;
    font-weight: 500;
    color: #5f6368;
    margin-bottom: 8px;
}

.source-reference {
    display: inline-block;
    background: #e8f0fe;
    color: #1a73e8;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    margin-right: 8px;
    margin-bottom: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.source-reference:hover {
    background: #d2e3fc;
}

/* 輸入區域 */
.chat-input-container {
    padding: 24px;
    border-top: 1px solid #e8eaed;
    background: white;
}

.chat-input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    max-width: 800px;
    margin: 0 auto;
}

#chat-input {
    flex: 1;
    border: 1px solid #dadce0;
    border-radius: 24px;
    padding: 12px 20px;
    font-size: 16px;
    font-family: inherit;
    resize: none;
    outline: none;
    min-height: 48px;
    max-height: 120px;
    transition: border-color 0.2s;
}

#chat-input:focus {
    border-color: #1a73e8;
}

.send-button {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: none;
    background: #1a73e8;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.send-button:hover:not(:disabled) {
    background: #1765cc;
    transform: scale(1.05);
}

.send-button:disabled {
    background: #dadce0;
    cursor: not-allowed;
}

/* 右側參數面板 */
.right-panel {
    width: 300px;
    background: white;
    border-left: 1px solid #e8eaed;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.collapse-btn {
    background: none;
    border: none;
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;
    color: #5f6368;
    transition: background-color 0.2s;
}

.collapse-btn:hover {
    background: #f1f3f4;
}

.params-content {
    flex: 1;
    overflow-y: auto;
    padding: 0 24px 140px; /* 調整底部padding適應縮小的按鈕 */
}

.param-section {
    margin-bottom: 32px;
}

.param-section h3 {
    font-size: 14px;
    font-weight: 500;
    color: #202124;
    margin-bottom: 16px;
}

.param-item {
    margin-bottom: 20px;
}

.param-item label {
    display: block;
    font-size: 14px;
    color: #5f6368;
    margin-bottom: 8px;
}

.param-item select,
.param-item input[type="range"] {
    width: 100%;
}

.param-item select {
    padding: 8px 12px;
    border: 1px solid #dadce0;
    border-radius: 4px;
    font-size: 14px;
    background: white;
    outline: none;
}

.param-item select:focus {
    border-color: #1a73e8;
}

.param-item input[type="range"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    height: 4px;
    background: #dadce0;
    border-radius: 2px;
    outline: none;
    width: 100%;
}

.param-item input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    background: #1a73e8;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.param-item input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: #1a73e8;
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.param-value {
    float: right;
    font-size: 14px;
    color: #1a73e8;
    font-weight: 500;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.checkbox-item input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #1a73e8;
}

.checkbox-item label {
    margin: 0;
    cursor: pointer;
}

/* 載入動畫 */
.loading-dots {
    display: flex;
    gap: 4px;
    align-items: center;
    padding: 12px 16px;
}

.loading-dot {
    width: 8px;
    height: 8px;
    background: #5f6368;
    border-radius: 50%;
    animation: loading-bounce 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dot:nth-child(3) { animation-delay: 0s; }

@keyframes loading-bounce {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 思考動畫容器 */
.thinking-animation {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 0;
}

.message.thinking .message-content {
    background: #f1f3f4;
    border: 1px solid #e8eaed;
}

/* 通知樣式 */
.notification {
    position: fixed;
    top: 80px;
    right: 24px;
    background: white;
    padding: 16px 20px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 12px;
    z-index: 1001;
    transform: translateX(100%);
    animation: slideIn 0.3s ease-out forwards;
    max-width: 400px;
}

.notification.success {
    border-left: 4px solid #34a853;
}

.notification.error {
    border-left: 4px solid #ea4335;
}

.notification.info {
    border-left: 4px solid #1a73e8;
}

.notification.fade-out {
    animation: slideOut 0.3s ease-in forwards;
}

@keyframes slideIn {
    to { transform: translateX(0); }
}

@keyframes slideOut {
    to { transform: translateX(100%); }
}

/* 工具提示 */
.tooltip {
    position: relative;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #202124;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
    margin-bottom: 8px;
}

.tooltip:hover::after {
    opacity: 1;
}

/* API 狀態指示器 */
.api-status-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: white;
    border-radius: 6px; /* 進一步縮小圓角 */
    padding: 4px 6px; /* 進一步縮小 padding */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
    display: flex;
    gap: 4px; /* 進一步縮小間距 */
    z-index: 1000;
    border: 1px solid #e8eaed;
    font-size: 12px; /* 恢復字體大小 */
}

.status-item {
    display: flex;
    align-items: center;
    gap: 3px; /* 進一步縮小間距 */
    cursor: pointer;
    padding: 2px 3px; /* 進一步縮小 padding */
    border-radius: 3px; /* 進一步縮小圓角 */
    transition: background-color 0.2s;
}

.status-item:hover {
    background: #f1f3f4;
}

.status-dot {
    width: 6px; /* 縮小狀態點 */
    height: 6px;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.status-dot::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.8);
    opacity: 0;
    transition: opacity 0.3s;
}

/* 狀態顏色 */
.status-dot.online {
    background: #34a853;
    box-shadow: 0 0 8px rgba(52, 168, 83, 0.4);
}

.status-dot.online::after {
    opacity: 1;
}

.status-dot.offline {
    background: #ea4335;
    box-shadow: 0 0 8px rgba(234, 67, 53, 0.4);
}

.status-dot.checking {
    background: #fbbc04;
    box-shadow: 0 0 8px rgba(251, 188, 4, 0.4);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
}

.status-label {
    font-weight: 500;
    color: #5f6368;
    font-size: 12px; /* 恢復字體大小 */
    white-space: nowrap;
    letter-spacing: -0.2px; /* 添加負字間距讓文字更緊湊 */
}

/* 隱藏元素 */
.hidden {
    display: none !important;
}

/* 響應式設計 */
@media (max-width: 1200px) {
    .right-panel {
        width: 280px;
    }

    .param-actions {
        width: 280px;
        bottom: 60px;
    }
}

@media (max-width: 1024px) {
    .left-panel {
        width: 280px;
    }

    .right-panel {
        width: 260px;
    }

    .param-actions {
        width: 260px;
        bottom: 60px;
    }
}

@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
    }

    .left-panel,
    .right-panel {
        width: 100%;
        height: auto;
        position: fixed;
        top: 64px;
        left: 0;
        right: 0;
        z-index: 200;
        transform: translateY(-100%);
        transition: transform 0.3s ease;
    }

    .left-panel.open,
    .right-panel.open {
        transform: translateY(0);
    }

    .chat-area {
        width: 100%;
    }

    .param-actions {
        width: 100%;
        position: relative;
        bottom: auto;
        right: auto;
    }

    .top-header {
        padding: 0 16px;
    }

    .logo h1 {
        font-size: 18px;
    }

    .chat-input-container {
        padding: 16px;
    }

    .notification {
        right: 16px;
        left: 16px;
        transform: translateY(-100%);
    }

    @keyframes slideIn {
        to { transform: translateY(0); }
    }

    @keyframes slideOut {
        to { transform: translateY(-100%); }
    }
}

@media (max-width: 480px) {
    .top-header {
        padding: 0 12px;
    }

    .logo .material-icons {
        font-size: 24px;
    }

    .logo h1 {
        font-size: 16px;
    }

    .chat-input-container {
        padding: 12px;
    }

    .welcome-message {
        padding: 40px 20px;
    }

    .welcome-message h3 {
        font-size: 20px;
    }

    /* 手機端 API 狀態指示器調整 */
    .api-status-indicator {
        bottom: 16px;
        right: 16px;
        padding: 6px 8px;
        gap: 8px;
    }

    .status-label {
        font-size: 10px;
    }

    .status-dot {
        width: 6px;
        height: 6px;
    }
}

/* 右上角設置按鈕樣式已在 header 中定義 */

/* 連線設置彈窗 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    justify-content: center;
    align-items: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e8eaed;
}

.modal-header h3 {
    margin: 0;
    color: #202124;
    font-size: 18px;
    font-weight: 500;
}

.close-btn {
    background: none;
    border: none;
    color: #5f6368;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #f1f3f4;
    color: #d93025;
}

.modal-body {
    padding: 20px 24px;
}

.setting-section {
    margin-bottom: 24px;
}

.setting-section h4 {
    margin: 0 0 12px 0;
    color: #202124;
    font-size: 14px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.setting-item {
    margin-bottom: 16px;
}

.setting-item label {
    display: block;
    margin-bottom: 6px;
    color: #3c4043;
    font-size: 14px;
    font-weight: 500;
}

.setting-item input[type="text"] {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #dadce0;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
    box-sizing: border-box;
}

.setting-item input[type="text"]:focus {
    outline: none;
    border-color: #1a73e8;
    box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
}

.setting-item small {
    display: block;
    margin-top: 4px;
    color: #5f6368;
    font-size: 12px;
}

/* 連線狀態 */
.connection-status {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
}

.status-label {
    font-weight: 500;
    color: #3c4043;
    min-width: 80px;
}

.status-indicator {
    flex: 1;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}

.status-indicator.connected {
    background: #e8f5e8;
    color: #137333;
}

.status-indicator.disconnected {
    background: #fce8e6;
    color: #d93025;
}

.status-indicator.testing {
    background: #fef7e0;
    color: #ea8600;
}

.test-btn {
    background: #1a73e8;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.test-btn:hover {
    background: #1557b0;
}

.test-btn:disabled {
    background: #dadce0;
    cursor: not-allowed;
}

/* 預設按鈕 */
.preset-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.preset-btn {
    background: #f8f9fa;
    border: 1px solid #dadce0;
    color: #3c4043;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.preset-btn:hover {
    background: #e8f0fe;
    border-color: #1a73e8;
    color: #1a73e8;
}

.preset-btn.active {
    background: #1a73e8;
    border-color: #1a73e8;
    color: white;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 24px 20px;
    border-top: 1px solid #e8eaed;
}

.btn-primary, .btn-secondary {
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
}

.btn-primary {
    background: #1a73e8;
    color: white;
}

.btn-primary:hover {
    background: #1557b0;
}

.btn-secondary {
    background: #f8f9fa;
    color: #3c4043;
    border: 1px solid #dadce0;
}

.btn-secondary:hover {
    background: #e8f0fe;
    border-color: #1a73e8;
    color: #1a73e8;
}

/* 通知動畫 */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 參數操作按鈕樣式 */
.param-actions {
    position: fixed;
    bottom: 60px; /* 從底部往上移 60px */
    right: 0;
    width: 300px;
    background: white;
    border-top: 1px solid #e8eaed;
    border-radius: 8px 0 0 8px; /* 添加左側圓角 */
    padding: 12px 20px; /* 縮小 padding */
    display: flex;
    flex-direction: column;
    gap: 6px; /* 縮小按鈕間距 */
    box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.15);
    z-index: 100;
}

.action-btn {
    padding: 8px 12px; /* 縮小 padding */
    border-radius: 4px; /* 縮小圓角 */
    font-size: 13px; /* 縮小字體 */
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px; /* 縮小間距 */
}

.action-btn.primary {
    background: #1a73e8;
    color: white;
}

.action-btn.primary:hover {
    background: #1557b0;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(26, 115, 232, 0.3);
}

.action-btn.secondary {
    background: #f8f9fa;
    color: #5f6368;
    border: 1px solid #dadce0;
}

.action-btn.secondary:hover {
    background: #e8f0fe;
    border-color: #1a73e8;
    color: #1a73e8;
}

.action-btn:disabled {
    background: #dadce0;
    color: #9aa0a6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 參數載入狀態 */
.param-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #5f6368;
    font-size: 14px;
}

.param-loading .spinner {
    margin-right: 8px;
}

/* 參數更新成功提示 */
.param-success {
    background: #e8f5e8;
    color: #2e7d32;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.param-error {
    background: #ffebee;
    color: #c62828;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 說明書彈窗樣式 */
.help-guide-content {
    max-width: 800px;
    width: 90%;
    max-height: 85vh;
}

.help-guide-body {
    max-height: 70vh;
    overflow-y: auto;
}

.help-section {
    margin-bottom: 32px;
    border-bottom: 1px solid #f1f3f4;
    padding-bottom: 24px;
}

.help-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.help-section h4 {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #1a73e8;
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 16px;
}

.help-section h4 .material-icons {
    font-size: 20px;
}

.help-item {
    margin-bottom: 20px;
}

.help-item h5 {
    color: #202124;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
}

.help-item p {
    color: #5f6368;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 6px;
}

.help-tip {
    background: #e8f0fe;
    color: #1a73e8;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    margin-top: 8px;
    border-left: 3px solid #1a73e8;
}

.help-scenarios {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
    margin-top: 16px;
}

.scenario {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #34a853;
}

.scenario h5 {
    color: #202124;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 8px;
}

.scenario p {
    color: #5f6368;
    font-size: 13px;
    font-family: monospace;
    background: white;
    padding: 8px;
    border-radius: 4px;
    margin: 0;
}

/* RAG 架構圖彈窗樣式 */
.rag-diagram-modal {
    max-width: 1200px;
    width: 95%;
    max-height: 90vh;
}

.rag-diagram-container {
    margin-bottom: 20px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    padding: 10px;
    background: #fafafa;
    overflow: auto;
}

.diagram-image-wrapper {
    position: relative;
    width: 100%;
    max-width: 1000px;
    margin: 0 auto;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.diagram-background-image {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 8px;
}

.param-overlay {
    background: transparent;
    border: none;
    padding: 0;
    margin: 0;
    width: auto;
    height: 24px;
    z-index: 10;
    pointer-events: auto;
    display: flex;
    align-items: center;
    gap: 4px;
}

.param-label {
    font-size: 11px;
    color: #333;
    font-weight: 500;
    white-space: nowrap;
}

.param-overlay:hover,
.param-overlay:focus,
.param-overlay:active {
    transform: none !important;
    box-shadow: none !important;
    border: none !important;
    background: transparent !important;
}

.param-overlay input:hover,
.param-overlay input:focus,
.param-overlay input:active {
    transform: none !important;
    box-shadow: none !important;
}

.diagram-input:hover,
.diagram-input:focus,
.diagram-input:active {
    transform: none !important;
    box-shadow: none !important;
}

/* 移除不需要的標題和標籤樣式 */
.param-overlay-title {
    display: none;
}

.param-overlay-label {
    display: none;
}

.llm-params {
    min-width: 120px;
    max-width: 140px;
}

.llm-param-group {
    margin-bottom: 10px;
    padding: 4px 0;
}

.llm-param-group:last-child {
    margin-bottom: 0;
}

.llm-param-group .param-overlay-label {
    font-size: 9px;
    margin-bottom: 3px;
    color: #1565c0;
}

.rrf-param-group {
    margin-bottom: 8px;
    padding: 2px 0;
}

.rrf-param-group:last-child {
    margin-bottom: 0;
}

.rrf-param-group .param-overlay-label {
    font-size: 9px;
    margin-bottom: 3px;
    color: #6a1b9a;
}

.diagram-placeholder {
    width: 100%;
    height: 400px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border: 2px dashed #ccc;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.placeholder-content {
    padding: 40px;
    color: #666;
}

.placeholder-content h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 24px;
}

.placeholder-content p {
    margin: 10px 0;
    font-size: 16px;
    line-height: 1.5;
}

.placeholder-content code {
    background: rgba(0,0,0,0.1);
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
}

.placeholder-content small {
    color: #999;
    font-size: 14px;
}

.diagram-input {
    width: 50px;
    padding: 2px 4px;
    border: 2px solid;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    background: rgba(255, 255, 255, 0.9);
    box-sizing: border-box;
    color: #333;
}

.diagram-input:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.diagram-input:hover {
    /* 移除 hover 效果 */
}

/* 各模組對應顏色 */
.vector-param .diagram-input {
    border-color: #28a745; /* 綠色 - 向量檢索 */
}

.bm25-param .diagram-input {
    border-color: #ffc107; /* 黃色 - BM25 */
}

.rrf-param .diagram-input {
    border-color: #fd7e14; /* 橘色 - RRF */
}

.rerank-param .diagram-input {
    border-color: #6f42c1; /* 紫色 - 重排序 */
}

.llm-param .diagram-input {
    border-color: #007bff; /* 藍色 - LLM */
}

/* 參數框的脈動動畫效果 */
@keyframes param-pulse {
    0% { box-shadow: 0 4px 12px rgba(0,0,0,0.15); }
    50% { box-shadow: 0 6px 16px rgba(33, 150, 243, 0.2); }
    100% { box-shadow: 0 4px 12px rgba(0,0,0,0.15); }
}

.param-overlay.active {
    animation: param-pulse 2s infinite;
}

/* 舊的顏色樣式已移除，使用新的類別樣式 */

.diagram-controls {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.control-section h4 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-section .form-group {
    margin-bottom: 12px;
}

.control-section label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #555;
    font-size: 14px;
}

.control-section .param-input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
}

.control-section .param-input:focus {
    outline: none;
    border-color: #2196F3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.control-section input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
    transform: scale(1.2);
}

.control-section input[type="range"] {
    width: 70%;
    margin-right: 8px;
}

.transparency-value {
    font-size: 12px;
    color: #666;
    font-weight: 600;
    min-width: 40px;
    display: inline-block;
}

.transparency-presets {
    display: flex;
    gap: 4px;
    margin-top: 8px;
    flex-wrap: wrap;
}

.transparency-preset-btn {
    background: #f8f9fa;
    border: 1px solid #dadce0;
    color: #5f6368;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 60px;
}

.transparency-preset-btn:hover {
    background: #e8f0fe;
    border-color: #1a73e8;
    color: #1a73e8;
}

.transparency-preset-btn.active {
    background: #1a73e8;
    border-color: #1a73e8;
    color: white;
}

.form-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 20px;
}

.form-actions .btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    display: flex;
    align-items: center;
    gap: 6px;
}

.status-message {
    margin-top: 15px;
    padding: 10px;
    border-radius: 6px;
    font-size: 14px;
    text-align: center;
    display: none;
}

.status-message.success {
    background: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #c8e6c9;
    display: block;
}

.status-message.error {
    background: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
    display: block;
}

/* 響應式設計 - RAG 架構圖 */
@media (max-width: 768px) {
    .rag-diagram-modal {
        width: 98%;
        max-height: 95vh;
    }

    .diagram-controls {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .rag-diagram-svg {
        max-height: 300px;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
        justify-content: center;
    }
}
