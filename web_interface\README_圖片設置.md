# 🖼️ RAG 架構圖設置指南

## 📋 快速設置步驟

### 1. 準備您的圖片
- 將您的 RAG 架構圖保存為 `rag-diagram.png` 或 `rag-diagram.jpg`
- 建議尺寸：1000x600 像素（或保持相同比例）
- 確保圖片清晰，各組件位置明確

### 2. 放置圖片文件
```
web_interface/
├── index.html
├── script.js
├── styles.css
└── rag-diagram.png  ← 將您的圖片放在這裡
```

### 3. 調整參數位置
在 `index.html` 中找到參數覆蓋層，根據您圖片中的實際位置調整百分比：

```html
<!-- Vector Index top_k -->
<div class="param-overlay" style="position: absolute; top: 18%; left: 55%;">
    <label class="param-overlay-label">top_k</label>
    <input type="number" class="param-input diagram-input" id="diagram-vector-top-k" min="1" max="50" value="10"/>
</div>
```

## 🎯 參數位置對應表

| 組件 | 標題 | 參數 | 當前位置 | 說明 |
|------|------|------|----------|------|
| Vector Index | 🔍 向量檢索 | top_k | top: 18%, left: 55% | 向量檢索數量 |
| Sparse Index (BM25) | 📊 BM25 檢索 | top_k | top: 35%, left: 55% | BM25 檢索數量 |
| RRF | 🔄 結果融合 | RRF k | top: 27%, left: 75% | 融合參數 |
| Reranker | 🎯 重排序 | top_k | top: 27%, left: 90% | 重排序數量 |
| LLM | 🤖 語言模型 | 溫度 & 最大 tokens | top: 50%, left: 95% | 生成參數 |

## 🔧 位置調整技巧

### 使用瀏覽器開發者工具
1. 按 F12 打開開發者工具
2. 找到 `.param-overlay` 元素
3. 在 Styles 面板中實時調整 `top` 和 `left` 值
4. 找到合適位置後，複製到 HTML 文件中

### 位置參考
- `top: 0%` = 圖片頂部
- `top: 50%` = 圖片中央
- `top: 100%` = 圖片底部
- `left: 0%` = 圖片左側
- `left: 50%` = 圖片中央
- `left: 100%` = 圖片右側

## 🎨 樣式自定義

### 修改參數框樣式
在 `styles.css` 中找到 `.param-overlay` 類別：

```css
.param-overlay {
    background: rgba(255, 255, 255, 0.95);  /* 背景透明度 */
    border: 2px solid #2196F3;              /* 邊框顏色 */
    border-radius: 6px;                     /* 圓角大小 */
    padding: 8px;                           /* 內邊距 */
    /* ... 其他樣式 */
}
```

### 修改懸停效果
```css
.param-overlay:hover {
    transform: translate(-50%, -50%) scale(1.1) !important;  /* 放大倍數 */
    border-color: #1976d2;                                   /* 懸停邊框色 */
    /* ... 其他樣式 */
}
```

## 🚀 測試和驗證

1. 刷新瀏覽器頁面
2. 點擊頂部的 🌳 圖標打開 RAG 架構圖
3. 檢查參數框是否正確對應到圖片中的組件位置
4. 測試參數輸入和應用功能

## 💡 常見問題

### Q: 圖片不顯示怎麼辦？
A: 檢查文件名是否正確，確保是 `rag-diagram.png` 或 `rag-diagram.jpg`

### Q: 參數框位置不對怎麼辦？
A: 使用開發者工具調整 `top` 和 `left` 百分比值

### Q: 可以使用其他圖片格式嗎？
A: 可以，但需要修改 HTML 中的 `src` 屬性

### Q: 圖片太大或太小怎麼辦？
A: CSS 會自動縮放，但建議使用 1000x600 像素的圖片以獲得最佳效果

## 📞 需要幫助？

如果您在設置過程中遇到問題，請：
1. 檢查瀏覽器控制台是否有錯誤信息
2. 確認文件路徑和名稱正確
3. 使用開發者工具檢查元素位置
