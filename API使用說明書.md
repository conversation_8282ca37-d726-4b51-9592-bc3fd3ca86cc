# RAG 智能查詢系統 API 使用說明書

## 📋 概述

本文檔提供 RAG 智能查詢系統**後端 API** 的完整使用指南，包括文件管理 API 和查詢 API 的詳細說明、示例代碼和最佳實踐。

**重要說明**：
- 本文檔僅針對 **rag_query_tool** 中的 FastAPI 後端服務
- **web_interface** 是純靜態網頁，不提供 API 服務
- 前端通過 JavaScript 調用這些後端 API

---

## 🌐 API 基本信息

### 服務地址
- **後端 API 服務**：`http://localhost:8000` (本地開發)
- **生產環境**：`https://your-api-service.a.run.app` (Cloud Run 部署後)

### 認證方式
- 當前版本無需認證
- 生產環境建議添加 API Key 或 JWT 認證

### 數據格式
- 請求格式：`application/json`
- 響應格式：`application/json`
- 文件上傳：`multipart/form-data`

---

## 📁 文件管理 API

### 1. 上傳文件

**端點**：`POST /upload`

**描述**：上傳文檔文件並自動處理（解析、分塊、向量化、存儲）

**支持格式**：PDF、TXT、DOCX

**請求參數**：
```http
Content-Type: multipart/form-data
```

| 參數 | 類型 | 必填 | 描述 |
|------|------|------|------|
| file | File | 是 | 要上傳的文檔文件 |

**響應示例**：
```json
{
  "message": "文件上傳成功",
  "file_id": "550e8400-e29b-41d4-a716-446655440000",
  "filename": "example.pdf",
  "status": "success"
}
```

**錯誤響應**：
```json
{
  "detail": "不支援的文件格式。支援的格式: .pdf, .txt, .docx"
}
```

**cURL 示例**：
```bash
curl -X POST "http://localhost:8000/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/your/document.pdf"
```

**Python 示例**：
```python
import requests

url = "http://localhost:8000/upload"
files = {"file": open("document.pdf", "rb")}

response = requests.post(url, files=files)
print(response.json())
```

### 2. 獲取文件列表

**端點**：`GET /files`

**描述**：獲取已上傳的文件列表

**響應示例**：
```json
{
  "files": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "filename": "example.pdf",
      "size": 1024000,
      "upload_time": "2024-01-01T12:00:00Z",
      "status": "processed"
    }
  ],
  "total": 1
}
```

**cURL 示例**：
```bash
curl -X GET "http://localhost:8000/files"
```

### 3. 刪除文件

**端點**：`DELETE /files/{file_id}`

**描述**：刪除指定文件及其相關數據

**路徑參數**：
| 參數 | 類型 | 必填 | 描述 |
|------|------|------|------|
| file_id | string | 是 | 文件唯一標識符 |

**響應示例**：
```json
{
  "message": "文件刪除成功",
  "file_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

**cURL 示例**：
```bash
curl -X DELETE "http://localhost:8000/files/550e8400-e29b-41d4-a716-446655440000"
```

---

## 🔍 查詢 API

### 1. 智能查詢

**端點**：`POST /query`

**描述**：執行 RAG 智能查詢，返回基於文檔內容的 AI 生成回答

**請求參數**：
```json
{
  "query_text": "查詢文本",
  "top_k": 5,
  "temperature": 0.7,
  "max_tokens": 1000,
  "show_sources": true
}
```

| 參數 | 類型 | 必填 | 默認值 | 描述 |
|------|------|------|--------|------|
| query_text | string | 是 | - | 用戶查詢文本 |
| top_k | integer | 否 | 5 | 檢索文檔數量 (1-20) |
| temperature | float | 否 | 0.7 | LLM 生成溫度 (0.0-2.0) |
| max_tokens | integer | 否 | 1000 | 最大生成 token 數 |
| show_sources | boolean | 否 | false | 是否顯示來源文檔 |

**響應示例**：
```json
{
  "response": "根據您上傳的文檔，貓咪的飲食需要注意以下幾點：\n\n1. 提供高質量的貓糧\n2. 確保充足的水分攝取\n3. 避免給貓咪餵食人類食物...",
  "sources": [
    {
      "document_id": "doc_123",
      "content": "相關文檔片段...",
      "similarity_score": 0.95
    }
  ],
  "query_time": 2.34
}
```

**cURL 示例**：
```bash
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query_text": "貓咪飲食注意事項",
    "top_k": 5,
    "temperature": 0.7,
    "show_sources": true
  }'
```

**Python 示例**：
```python
import requests

url = "http://localhost:8000/query"
data = {
    "query_text": "貓咪飲食注意事項",
    "top_k": 5,
    "temperature": 0.7,
    "show_sources": True
}

response = requests.post(url, json=data)
result = response.json()
print(result["response"])
```

**JavaScript 示例**：
```javascript
const queryData = {
    query_text: "貓咪飲食注意事項",
    top_k: 5,
    temperature: 0.7,
    show_sources: true
};

fetch('http://localhost:8000/query', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify(queryData)
})
.then(response => response.json())
.then(data => console.log(data.response));
```

---

## 🏥 健康檢查 API

### 健康狀態檢查

**端點**：`GET /health`

**描述**：檢查服務運行狀態

**響應示例**：
```json
{
  "status": "healthy",
  "service": "RAG Query API",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**cURL 示例**：
```bash
curl -X GET "http://localhost:8000/health"
```

---

## 📊 API 文檔

### Swagger UI
訪問 `http://localhost:8000/docs` 查看交互式 API 文檔

### ReDoc
訪問 `http://localhost:8000/redoc` 查看 ReDoc 格式文檔

---

## ⚠️ 錯誤處理

### 常見錯誤碼

| 狀態碼 | 錯誤類型 | 描述 |
|--------|----------|------|
| 400 | Bad Request | 請求參數錯誤 |
| 404 | Not Found | 資源不存在 |
| 413 | Payload Too Large | 文件過大 |
| 415 | Unsupported Media Type | 不支援的文件格式 |
| 500 | Internal Server Error | 服務器內部錯誤 |

### 錯誤響應格式
```json
{
  "detail": "錯誤描述信息",
  "error_code": "ERROR_CODE",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

---

## 🔧 最佳實踐

### 1. 文件上傳建議
- **文件大小**：建議單個文件不超過 50MB
- **文件格式**：優先使用 PDF 格式，文字識別效果最佳
- **文件命名**：使用有意義的文件名，便於管理

### 2. 查詢優化建議
- **查詢文本**：使用清晰、具體的查詢語句
- **參數調整**：
  - `top_k`: 3-10 之間效果較佳
  - `temperature`: 0.3-0.8 之間平衡創造性和準確性
- **批量查詢**：避免短時間內大量請求

### 3. 性能優化
- **緩存機制**：相同查詢會利用緩存加速響應
- **並發限制**：建議同時查詢數不超過 10 個
- **超時設置**：建議設置 30 秒超時時間

---

## 🔒 安全注意事項

### 1. 數據安全
- 上傳的文件會在本地處理和存儲
- 不會將數據發送到外部服務
- 建議在內網環境部署

### 2. 訪問控制
- 生產環境建議添加認證機制
- 可配置 IP 白名單限制訪問
- 建議使用 HTTPS 協議

---

## 📞 技術支持

### 常見問題排查
1. **服務無法訪問**：檢查服務是否正常啟動
2. **文件上傳失敗**：確認文件格式和大小
3. **查詢無結果**：確認已上傳相關文檔
4. **響應速度慢**：檢查服務器資源使用情況

### 日誌查看
```bash
# 查看服務日誌
docker logs rag-api-container

# 實時監控日誌
docker logs -f rag-api-container
```

### 聯繫方式
- 技術文檔：查看項目 README.md
- 問題反饋：提交 GitHub Issue
- 緊急支持：聯繫系統管理員

---

## 📝 更新日誌

### v1.0.0 (2024-01-01)
- 初始版本發布
- 支持基本文件上傳和查詢功能
- 實現混合檢索和重排序

### 後續版本規劃
- 添加用戶認證機制
- 支持更多文件格式
- 實現流式查詢響應
- 添加查詢分析功能

---

*本文檔會隨著 API 版本更新而持續維護，請關注最新版本。*
