# RAG Query Tool

一個基於 FastAPI 的 RAG (Retrieval-Augmented Generation) 查詢工具，整合了向量檢索、BM25 稀疏檢索、重排序和 LLM 生成功能。

## 🚀 功能特色

- 🔍 **混合檢索**: 結合向量相似度和 BM25 稀疏檢索
- 🔄 **RRF 融合**: 使用 Reciprocal Rank Fusion 優化檢索結果
- 🎯 **智能重排序**: 使用 ONNX 重排序模型提升相關性
- 🤖 **LLM 生成**: 整合 Gemini API 生成高質量回答
- ⚡ **高性能**: 異步處理和優化的查詢流程

## 📦 快速開始

### Docker 部署 (推薦)

```bash
# 構建映像
docker build -t rag-api-app .

# 運行容器
docker run -d -p 8001:8001 --name rag-api-V1 rag-api-app

# 檢查狀態
curl http://localhost:8001/health
```

### 本地開發

```bash
# 安裝依賴
poetry install

# 配置環境變數 (.env 文件)
cp .env.example .env

# 啟動服務
poetry run uvicorn rag_query_tool.api:app --host 0.0.0.0 --port 8001
```

## 🔧 API 使用

### 基本查詢

```bash
curl -X POST "http://localhost:8001/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query_text": "什麼是貓咪的特徵？",
    "include_llm_response": true
  }'
```

### 獲取原始文檔

```bash
curl -X POST "http://localhost:8001/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query_text": "貓咪照護",
    "raw_documents_only": true
  }'
```

## 📁 項目結構

```
rag_query_tool/
├── api.py              # FastAPI 應用主程序
├── query_tool.py       # 核心 RAG 查詢邏輯
├── config/             # 配置管理模組
├── database/           # 數據庫模型和連接
├── embeddings/         # 嵌入模型和向量處理
├── llm/               # LLM 生成器
├── retrieval/         # 檢索組件 (BM25, 重排序)
└── utils/             # 工具函數

web_interface/          # Web 前端界面 (獨立部署)
├── index.html         # 主頁面
├── script.js          # 前端邏輯
└── styles.css         # 樣式文件
```

## ⚙️ 環境配置

創建 `.env` 文件：

```env
# 數據庫配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=your_database
DB_USER=postgres
DB_PASSWORD=your_password

# API 配置
GEMINI_API_KEY=your_gemini_api_key

# 模型路徑
RERANK_ONNX_PATH=rag_query_tool/embeddings/BAAI--bge-reranker-base/onnx_model/model.int8.onnx
LOCAL_EMBEDDING_MODEL_PATH=rag_query_tool/embeddings/bge-base-zh-v1.5
```

## 🌐 訪問端點

- **API 文檔**: http://localhost:8001/docs
- **健康檢查**: http://localhost:8001/health
- **配置信息**: http://localhost:8001/config

## 📚 文檔

- [API 使用說明書](API使用說明書.md)
- [API 使用範例](API_使用範例.md)
- [VM 部署指南](VM_部署指南.md)
- [Cloud Run 部署指南](Cloud_Run_部署指南.md)

## 📄 許可證

MIT License
