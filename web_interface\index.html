<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG 智能問答系統</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Google+Sans:wght@400;500;700&family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
</head>
<body>
    <!-- 頂部導航欄 -->
    <header class="top-header">
        <div class="header-content">
            <div class="logo">
                <span class="material-icons">auto_stories</span>
                <h1>RAG 智能問答系統</h1>
            </div>
            <div class="header-actions">
                <button class="icon-button" id="rag-diagram-btn" title="RAG 架構圖調整">
                    <span class="material-icons">account_tree</span>
                </button>
                <button class="icon-button" id="connection-settings-btn" title="連線設置">
                    <span class="material-icons">settings</span>
                </button>
                <button class="icon-button" id="help-guide-btn" title="參數說明書">
                    <span class="material-icons">help_outline</span>
                </button>
            </div>
        </div>
    </header>

    <!-- 主要內容區域 -->
    <div class="main-container">
        <!-- 左側面板 - 資料管理 -->
        <aside class="left-panel">
            <div class="panel-header">
                <h2>來源</h2>
                <button class="add-source-btn" id="add-source-btn">
                    <span class="material-icons">add</span>
                    新增
                </button>
            </div>

            <!-- 文件上傳區域 -->
            <div class="upload-section" id="upload-section" style="display: none;">
                <div class="upload-zone" id="upload-zone">
                    <span class="material-icons">cloud_upload</span>
                    <p>拖拽文件到此處或點擊上傳</p>
                    <p class="upload-hint">支援 PDF, TXT, DOC, DOCX 格式</p>
                    <input type="file" id="file-input" accept=".pdf,.txt,.doc,.docx" style="display: none;">
                </div>
                <div class="upload-actions">
                    <button class="upload-btn" id="upload-btn" disabled>
                        <span class="material-icons">upload</span>
                        上傳並處理
                    </button>
                    <button class="cancel-btn" id="cancel-upload-btn">
                        <span class="material-icons">close</span>
                        取消
                    </button>
                </div>
            </div>

            <!-- 載入動畫 -->
            <div class="upload-loading" id="upload-loading" style="display: none;">
                <div class="loading-spinner"></div>
                <p>正在處理文件，請稍候...</p>
            </div>

            <!-- 上傳結果 -->
            <div class="upload-result" id="upload-result" style="display: none;"></div>

            <!-- 文件列表 -->
            <div class="sources-list" id="sources-list">
                <div class="sources-section">
                    <h3 class="sources-title">選取所有來源</h3>
                    <div class="file-list-content" id="file-list-content">
                        <div class="empty-state">
                            <p>尚未上傳任何資料</p>
                            <p class="hint">點擊上方按鈕開始上傳文件</p>
                        </div>
                    </div>
                </div>
                <button class="refresh-btn" id="refresh-files-btn" title="刷新列表">
                    <span class="material-icons">refresh</span>
                </button>
            </div>
        </aside>

        <!-- 中間對話區域 -->
        <main class="chat-area">
            <div class="chat-container">
                <div class="chat-messages" id="chat-messages">
                    <div class="welcome-message">
                        <div class="welcome-icon">
                            <span class="material-icons">psychology</span>
                        </div>
                        <h3>歡迎使用 RAG 智能問答系統</h3>
                        <p>您可以直接開始提問，系統會基於資料庫中的資料提供答案。也可以在左側上傳新的文件來擴充知識庫。</p>
                    </div>
                </div>

                <div class="chat-input-container">
                    <div class="chat-input-wrapper">
                        <textarea id="chat-input" placeholder="請輸入您的問題..." rows="1"></textarea>
                        <button id="send-button" class="send-button" disabled>
                            <span class="material-icons">send</span>
                        </button>
                    </div>
                </div>
            </div>
        </main>

        <!-- 右側參數調整面板 -->
        <aside class="right-panel">
            <div class="panel-header">
                <h2>參數設定</h2>
                <button class="collapse-btn" id="collapse-params">
                    <span class="material-icons">expand_less</span>
                </button>
            </div>

            <div class="params-content" id="params-content">
                <!-- 模型設定 -->
                <div class="param-section">
                    <h3>模型設定</h3>
                    <div class="param-item">
                        <label for="rerank-model-type">重排序模型</label>
                        <select id="rerank-model-type">
                            <option value="onnx_int8">ONNX INT8 (推薦)</option>
                            <option value="onnx_fp32">ONNX FP32</option>
                        </select>
                    </div>
                    <div class="param-item">
                        <label for="llm-model-type">LLM 模型</label>
                        <select id="llm-model-type">
                            <option value="gemini-2.0-flash">Gemini 2.0 Flash</option>
                            <option value="gemini-2.5-flash">Gemini 2.5 Flash</option>
                        </select>
                    </div>
                </div>

                <!-- 檢索設定 -->
                <div class="param-section">
                    <h3>檢索設定</h3>
                    <div class="param-item">
                        <label for="vector-top-k">向量檢索數量</label>
                        <input type="range" id="vector-top-k" min="1" max="50" value="10">
                        <span class="param-value">10</span>
                    </div>
                    <div class="param-item">
                        <label for="bm25-top-k">BM25 檢索數量</label>
                        <input type="range" id="bm25-top-k" min="1" max="50" value="10">
                        <span class="param-value">10</span>
                    </div>
                    <div class="param-item">
                        <label for="rerank-top-k">重排序數量</label>
                        <input type="range" id="rerank-top-k" min="1" max="20" value="5">
                        <span class="param-value">5</span>
                    </div>
                    <div class="param-item">
                        <label for="similarity-threshold">相似度閾值</label>
                        <input type="range" id="similarity-threshold" min="0" max="1" step="0.1" value="0.0">
                        <span class="param-value">0.0</span>
                    </div>
                </div>

                <!-- LLM 生成設定 -->
                <div class="param-section">
                    <h3>LLM 生成設定</h3>
                    <div class="param-item">
                        <label for="llm-temperature">創造性 (Temperature)</label>
                        <input type="range" id="llm-temperature" min="0" max="2" step="0.1" value="0.7">
                        <span class="param-value">0.7</span>
                    </div>
                    <div class="param-item">
                        <label for="llm-max-tokens">最大生成長度</label>
                        <input type="range" id="llm-max-tokens" min="100" max="4000" step="100" value="1000">
                        <span class="param-value">1000</span>
                    </div>
                    <div class="param-item">
                        <label for="max-context-length">最大上下文長度</label>
                        <input type="range" id="max-context-length" min="500" max="8000" step="500" value="3000">
                        <span class="param-value">3000</span>
                    </div>
                    <div class="param-item">
                        <label for="llm-top-p">Top-p 參數</label>
                        <input type="range" id="llm-top-p" min="0" max="1" step="0.1" value="0.9">
                        <span class="param-value">0.9</span>
                    </div>
                </div>

                <!-- 融合設定 -->
                <div class="param-section">
                    <h3>融合設定</h3>
                    <div class="param-item">
                        <label for="rrf-k">RRF 融合參數</label>
                        <input type="range" id="rrf-k" min="1" max="100" value="60">
                        <span class="param-value">60</span>
                    </div>
                    <div class="param-item">
                        <label for="rrf-top-k">RRF 傳給重排序數量</label>
                        <input type="range" id="rrf-top-k" min="5" max="30" value="15">
                        <span class="param-value">15</span>
                    </div>
                </div>

                <!-- 系統設定 -->
                <div class="param-section">
                    <h3>系統設定</h3>
                    <div class="param-item checkbox-item">
                        <input type="checkbox" id="enable-rerank" checked>
                        <label for="enable-rerank">啟用重排序</label>
                    </div>
                    <div class="param-item checkbox-item">
                        <input type="checkbox" id="enable-bm25" checked>
                        <label for="enable-bm25">啟用 BM25 檢索</label>
                    </div>
                    <div class="param-item checkbox-item">
                        <input type="checkbox" id="debug-mode">
                        <label for="debug-mode">調試模式</label>
                    </div>
                </div>

                <!-- 操作按鈕 -->
                <div class="param-section">
                    <div class="param-actions">
                        <button class="action-btn primary" id="apply-config">應用設定</button>
                        <button class="action-btn secondary" id="reset-config">重置默認</button>
                        <button class="action-btn secondary" id="load-config">載入配置</button>
                    </div>
                </div>
            </div>
        </aside>
    </div>

    <!-- API 配置彈窗 -->
    <div class="modal-overlay" id="api-config-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>API 設定</h3>
                <button class="close-btn" id="close-api-config">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="config-item">
                    <label for="api-base-url">API 基礎地址：</label>
                    <input type="text" id="api-base-url" value="http://localhost:8000" placeholder="例如：http://localhost:3000">
                    <small>請輸入您的文件處理 API 服務地址</small>
                </div>
                <div class="config-item">
                    <label for="query-api-url">查詢 API 地址：</label>
                    <input type="text" id="query-api-url" value="http://localhost:8000/query" placeholder="例如：http://localhost:8000/query">
                    <small>RAG 查詢服務的地址</small>
                </div>
            </div>
            <div class="modal-footer">
                <button class="save-btn" id="save-api-config">保存設定</button>
                <button class="cancel-btn" id="cancel-api-config">取消</button>
            </div>
        </div>
    </div>

    <!-- API 狀態指示器 -->
    <div class="api-status-indicator" id="api-status-indicator">
        <div class="status-item" id="file-api-status" title="文件處理 API (8000)">
            <div class="status-dot" id="file-api-dot"></div>
            <span class="status-label">文件</span>
        </div>
        <div class="status-item" id="query-api-status" title="RAG 查詢 API (8001)">
            <div class="status-dot" id="query-api-dot"></div>
            <span class="status-label">查詢</span>
        </div>
    </div>

    <!-- 連線設置彈窗 -->
    <div class="modal" id="connection-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>連線設置</h3>
                <button class="close-btn" id="close-connection-modal">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="setting-section">
                    <h4>API 端點設置</h4>
                    <div class="setting-item">
                        <label for="rag-api-url">RAG 查詢 API URL</label>
                        <input type="text" id="rag-api-url" placeholder="http://your-server.com:8001" />
                        <small>例如: http://34.80.123.45:8001 或 https://your-domain.com/api</small>
                    </div>
                    <div class="setting-item">
                        <label for="file-api-url">文件處理 API URL</label>
                        <input type="text" id="file-api-url" placeholder="http://your-server.com:8000" />
                        <small>例如: http://34.80.123.45:8000</small>
                    </div>
                </div>

                <div class="setting-section">
                    <h4>連線測試</h4>
                    <div class="connection-status">
                        <div class="status-item">
                            <span class="status-label">RAG API:</span>
                            <span class="status-indicator" id="rag-status">未測試</span>
                            <button class="test-btn" id="test-rag-btn">測試</button>
                        </div>
                        <div class="status-item">
                            <span class="status-label">文件 API:</span>
                            <span class="status-indicator" id="file-status">未測試</span>
                            <button class="test-btn" id="test-file-btn">測試</button>
                        </div>
                    </div>
                </div>

                <div class="setting-section">
                    <h4>環境預設</h4>
                    <div class="preset-buttons">
                        <button class="preset-btn" data-preset="local">本地開發</button>
                        <button class="preset-btn active" data-preset="proxy">Cloud Run 代理</button>
                        <button class="preset-btn" data-preset="vm">VM 直連</button>
                        <button class="preset-btn" data-preset="custom">自定義</button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" id="reset-settings-btn">重置</button>
                <button class="btn-primary" id="save-settings-btn">保存設置</button>
            </div>
        </div>
    </div>

    <!-- 參數說明書彈窗 -->
    <div class="modal" id="help-guide-modal">
        <div class="modal-content help-guide-content">
            <div class="modal-header">
                <h3>RAG 參數說明書</h3>
                <button class="close-btn" id="close-help-guide">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-body help-guide-body">
                <!-- 模型設定說明 -->
                <div class="help-section">
                    <h4><span class="material-icons">smart_toy</span>模型設定</h4>
                    <div class="help-item">
                        <h5>重排序模型 (Reranker)</h5>
                        <p><strong>作用：對檢索結果進行精確重新排序</strong></p>
                        <p><strong>ONNX INT8 (推薦)</strong>：</p>
                        <p>• 8位整數量化模型，文件大小約為原模型的 1/4</p>
                        <p>• 推理速度快 2-4 倍，內存使用減少 75%</p>
                        <p>• 精度損失極小（通常 < 1%）</p>
                        <p>• 適合生產環境和資源受限場景</p>
                        <p><strong>ONNX FP32</strong>：</p>
                        <p>• 32位浮點全精度模型，保持原始精度</p>
                        <p>• 推理速度較慢，內存使用較大</p>
                        <p>• 適合對精度要求極高的場景</p>
                        <div class="help-tip">💡 建議：一般使用選擇 INT8，追求極致精度選擇 FP32</div>
                    </div>
                    <div class="help-item">
                        <h5>LLM 模型 (大語言模型)</h5>
                        <p><strong>作用：基於檢索內容生成最終回答</strong></p>
                        <p><strong>Gemini 2.0 Flash</strong>：</p>
                        <p>• Google 穩定版本，經過大量測試驗證</p>
                        <p>• 回答質量穩定，適合生產環境</p>
                        <p>• 處理速度快，成本相對較低</p>
                        <p><strong>Gemini 2.5 Flash</strong>：</p>
                        <p>• Google 最新版本，推理能力顯著提升</p>
                        <p>• 對複雜問題理解更深入</p>
                        <p>• 支持更長的上下文處理</p>
                        <div class="help-tip">💡 建議：複雜問題使用 2.5，一般問題使用 2.0</div>
                    </div>
                </div>

                <!-- 檢索設定說明 -->
                <div class="help-section">
                    <h4><span class="material-icons">search</span>檢索設定</h4>
                    <div class="help-item">
                        <h5>向量檢索數量 (1-50)</h5>
                        <p><strong>語義相似度檢索</strong></p>
                        <p>• 使用向量嵌入技術，理解問題的語義含義</p>
                        <p>• 能找到意思相近但用詞不同的文檔</p>
                        <p>• 數量越多覆蓋面越廣，但速度越慢</p>
                        <p>• 適合概念性、抽象性問題</p>
                        <div class="help-tip">💡 建議：一般查詢 10-15，複雜查詢 20-30</div>
                    </div>
                    <div class="help-item">
                        <h5>BM25 檢索數量 (1-50)</h5>
                        <p><strong>關鍵詞精確匹配檢索</strong></p>
                        <p>• 基於 TF-IDF 的傳統檢索算法</p>
                        <p>• 擅長找到包含特定關鍵詞的文檔</p>
                        <p>• 對專有名詞、數字、日期等精確匹配效果好</p>
                        <p>• 與向量檢索互補，提高檢索召回率</p>
                        <div class="help-tip">💡 建議：與向量檢索數量保持一致，通常 10-15</div>
                    </div>
                    <div class="help-item">
                        <h5>重排序數量 (1-20)</h5>
                        <p><strong>最終傳給 LLM 的文檔數量</strong></p>
                        <p>• 經過重排序模型精選後的高質量文檔</p>
                        <p>• 直接影響 LLM 回答的質量和準確性</p>
                        <p>• 數量太多會引入噪音，太少會遺漏重要信息</p>
                        <p>• 需要在信息完整性和準確性間平衡</p>
                        <div class="help-tip">💡 建議：3-8 個最佳，簡單問題用 3-5，複雜問題用 5-8</div>
                    </div>
                    <div class="help-item">
                        <h5>相似度閾值 (0.0-1.0)</h5>
                        <p><strong>文檔質量過濾門檻</strong></p>
                        <p>• 過濾掉相似度低於閾值的文檔</p>
                        <p>• 0.0 表示不過濾，接受所有檢索結果</p>
                        <p>• 值越高過濾越嚴格，但可能遺漏相關信息</p>
                        <p>• 適合在文檔質量參差不齊時使用</p>
                        <div class="help-tip">💡 建議：一般設為 0.0，文檔質量差時可設 0.3-0.5</div>
                    </div>
                </div>

                <!-- LLM 生成設定說明 -->
                <div class="help-section">
                    <h4><span class="material-icons">psychology</span>LLM 生成設定</h4>
                    <div class="help-item">
                        <h5>創造性溫度 (0.0-2.0)</h5>
                        <p>控制回答的創造性和隨機性</p>
                        <p><strong>0.0-0.3</strong>：保守，回答一致性高</p>
                        <p><strong>0.4-0.8</strong>：平衡，適合大多數場景</p>
                        <p><strong>0.9-2.0</strong>：創造性強，回答多樣化</p>
                        <div class="help-tip">💡 建議：事實性問題用 0.3，創意性問題用 0.8</div>
                    </div>
                    <div class="help-item">
                        <h5>最大生成長度 (100-4000)</h5>
                        <p><strong>控制 LLM 輸出回答的最大長度</strong></p>
                        <p>• 限制 LLM 生成回答的最大 token 數</p>
                        <p>• 防止回答過長或無限生成</p>
                        <p>• 影響回答的詳細程度</p>
                        <div class="help-tip">💡 建議：簡短回答 500-1000，詳細回答 1500-3000</div>
                    </div>
                    <div class="help-item">
                        <h5>最大上下文長度 (500-8000)</h5>
                        <p><strong>控制輸入給 LLM 的文本長度</strong></p>
                        <p>• 包括：用戶問題 + 檢索到的相關文檔內容</p>
                        <p>• 如果檢索文檔太長，會被截斷到此長度</p>
                        <p>• 影響 LLM 能「看到」多少背景資料</p>
                        <p>• 長度越大，LLM 能參考更多資料，但計算成本更高</p>
                        <div class="help-tip">💡 建議：一般查詢 3000，複雜查詢需要更多背景時用 5000-8000</div>
                    </div>
                    <div class="help-item">
                        <h5>Top-p 參數 (0.0-1.0)</h5>
                        <p>控制詞彙選擇的多樣性，值越小選詞越保守</p>
                        <div class="help-tip">💡 建議：一般保持 0.9，追求穩定性可降至 0.7</div>
                    </div>
                </div>

                <!-- 融合設定說明 -->
                <div class="help-section">
                    <h4><span class="material-icons">merge_type</span>融合設定</h4>
                    <div class="help-item">
                        <h5>RRF 融合參數 (1-100)</h5>
                        <p>Reciprocal Rank Fusion 參數，影響多種檢索結果的融合</p>
                        <div class="help-tip">💡 建議：保持默認值 60，特殊需求可調整至 30-90</div>
                    </div>
                    <div class="help-item">
                        <h5>RRF 傳給重排序數量 (5-30)</h5>
                        <p>RRF 融合後傳遞給重排序模組的文檔數量</p>
                        <div class="help-tip">💡 建議：保持默認值 15，需要更多候選時可增加至 20-25</div>
                    </div>
                </div>

                <!-- 系統設定說明 -->
                <div class="help-section">
                    <h4><span class="material-icons">settings</span>系統設定</h4>
                    <div class="help-item">
                        <h5>啟用重排序</h5>
                        <p><strong>控制是否使用重排序模型精選檢索結果</strong></p>
                        <p>• <strong>開啟</strong>：使用 AI 重排序模型對檢索結果重新排序</p>
                        <p>• 能識別更相關的文檔，提升回答準確性</p>
                        <p>• 會增加少量處理時間，但顯著提升質量</p>
                        <p>• <strong>關閉</strong>：直接使用原始檢索排序</p>
                        <p>• 處理速度更快，但可能影響回答質量</p>
                        <div class="help-tip">💡 建議：強烈建議保持開啟，質量提升明顯</div>
                    </div>
                    <div class="help-item">
                        <h5>啟用 BM25 檢索</h5>
                        <p><strong>控制是否使用傳統關鍵詞檢索</strong></p>
                        <p>• <strong>開啟</strong>：同時使用向量檢索和 BM25 檢索</p>
                        <p>• 向量檢索擅長語義理解，BM25 擅長精確匹配</p>
                        <p>• 兩者結合能提高檢索召回率和準確性</p>
                        <p>• <strong>關閉</strong>：僅使用向量檢索</p>
                        <p>• 處理速度稍快，但可能遺漏關鍵詞匹配的文檔</p>
                        <div class="help-tip">💡 建議：保持開啟，兩種檢索方式互補效果好</div>
                    </div>
                    <div class="help-item">
                        <h5>調試模式</h5>
                        <p><strong>控制是否輸出詳細的系統運行信息</strong></p>
                        <p>• <strong>開啟</strong>：顯示檢索過程、文檔評分、處理時間等</p>
                        <p>• 幫助理解系統工作原理和優化參數</p>
                        <p>• 適合開發者調試和系統調優</p>
                        <p>• <strong>關閉</strong>：僅顯示最終回答結果</p>
                        <p>• 界面更簡潔，適合日常使用</p>
                        <div class="help-tip">💡 建議：開發調試時開啟，正常使用時關閉</div>
                    </div>
                </div>

                <!-- 使用建議 -->
                <div class="help-section">
                    <h4><span class="material-icons">lightbulb</span>使用建議</h4>
                    <div class="help-scenarios">
                        <div class="scenario">
                            <h5>🎯 精確查詢場景</h5>
                            <p><strong>適用：</strong>需要準確事實、具體數據、明確答案</p>
                            <p><strong>配置：</strong>向量檢索: 10, BM25: 10, 重排序: 3, 溫度: 0.3</p>
                            <p><strong>特點：</strong>回答保守準確，減少幻覺，適合法律、醫療、技術文檔查詢</p>
                        </div>
                        <div class="scenario">
                            <h5>🔍 探索性查詢場景</h5>
                            <p><strong>適用：</strong>研究複雜主題、需要全面了解、多角度分析</p>
                            <p><strong>配置：</strong>向量檢索: 20, BM25: 20, 重排序: 8, 溫度: 0.7</p>
                            <p><strong>特點：</strong>檢索範圍廣，信息豐富，適合學術研究、市場分析</p>
                        </div>
                        <div class="scenario">
                            <h5>⚡ 快速查詢場景</h5>
                            <p><strong>適用：</strong>日常問答、簡單查詢、追求響應速度</p>
                            <p><strong>配置：</strong>向量檢索: 5, BM25: 5, 重排序: 3, INT8 模型</p>
                            <p><strong>特點：</strong>處理速度快，資源消耗少，適合高頻使用</p>
                        </div>
                        <div class="scenario">
                            <h5>🎨 創意性查詢場景</h5>
                            <p><strong>適用：</strong>創意寫作、頭腦風暴、開放性討論</p>
                            <p><strong>配置：</strong>向量檢索: 15, 重排序: 5, 溫度: 0.8, Gemini 2.5</p>
                            <p><strong>特點：</strong>回答多樣化，創意性強，適合內容創作、策劃</p>
                        </div>
                    </div>
                    <div class="help-item">
                        <h5>📊 參數調優建議</h5>
                        <p><strong>回答太簡短</strong> → 增加重排序數量 (5-8) 或上下文長度</p>
                        <p><strong>回答不準確</strong> → 降低溫度 (0.1-0.3) 或增加檢索數量</p>
                        <p><strong>處理太慢</strong> → 減少檢索數量，使用 INT8 模型</p>
                        <p><strong>找不到相關內容</strong> → 增加檢索數量 (20-30) 或降低相似度閾值</p>
                        <p><strong>回答太保守</strong> → 提高溫度 (0.7-1.0) 或使用 Gemini 2.5</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- RAG 架構圖調整彈窗 -->
    <div id="rag-diagram-modal" class="modal">
        <div class="modal-content rag-diagram-modal">
            <div class="modal-header">
                <h3>🏗️ RAG 架構圖參數調整</h3>
                <span class="close" id="rag-diagram-close">&times;</span>
            </div>
            <div class="modal-body">
                <div class="rag-diagram-container">
                    <div class="diagram-image-wrapper">
                        <!-- 您的 RAG 架構圖作為背景 -->
                        <img src="rag-diagram.png" alt="RAG Architecture Diagram" class="diagram-background-image"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">

                        <!-- 圖片載入失敗時的佔位符 -->
                        <div class="diagram-placeholder" style="display: none;">
                            <div class="placeholder-content">
                                <h3>📊 請添加您的 RAG 架構圖</h3>
                                <p>將您的圖片保存為 <code>rag-diagram.png</code> 並放到 web_interface 目錄下</p>
                                <p>然後刷新頁面即可看到您的架構圖</p>
                                <small>建議尺寸：1000x600 像素</small>
                            </div>
                        </div>

                        <!-- 參數輸入框覆蓋在圖片上的對應位置 -->
                        <!-- Vector Index top_k - 綠色，精準對應綠色圓圈 -->
                        <div class="param-overlay vector-param" style="position: absolute; top: 27%; left: 61%;">
                            <input type="number" class="param-input diagram-input" id="diagram-vector-top-k" min="1" max="50" value="10"/>
                        </div>

                        <!-- BM25 top_k - 黃色，精準對應紫色圓圈 -->
                        <div class="param-overlay bm25-param" style="position: absolute; top: 41%; left: 61%;">
                            <input type="number" class="param-input diagram-input" id="diagram-bm25-top-k" min="1" max="50" value="10"/>
                        </div>

                        <!-- RRF k 參數 - 橘色，對應 RRF 框上半部 -->
                        <div class="param-overlay rrf-param" style="position: absolute; top: 30%; left: 69%;">
                            <input type="number" class="param-input diagram-input" id="diagram-rrf-k" min="1" max="100" value="60"/>
                        </div>

                        <!-- RRF top_k 參數 - 橘色，對應 RRF 框下半部 -->
                        <div class="param-overlay rrf-param" style="position: absolute; top: 33%; left: 77%;">
                            <input type="number" class="param-input diagram-input" id="diagram-rrf-top-k" min="5" max="30" value="15"/>
                        </div>

                        <!-- Reranker top_k - 紫色，對應右側紫色圓圈 -->
                        <div class="param-overlay rerank-param" style="position: absolute; top: 45%; left: 77%;">
                            <input type="number" class="param-input diagram-input" id="diagram-rerank-top-k" min="1" max="20" value="5"/>
                        </div>

                        <!-- LLM 溫度 - 藍色，對應藍色虛線框上方 -->
                        <div class="param-overlay llm-param" style="position: absolute; top: 61%; left: 81%;">
                            <span class="param-label">溫度:</span>
                            <input type="number" class="param-input diagram-input" id="diagram-llm-temperature" min="0" max="2" step="0.1" value="0.0"/>
                        </div>

                        <!-- LLM max tokens - 藍色，對應藍色虛線框下方 -->
                        <div class="param-overlay llm-param" style="position: absolute; top: 66%; left: 81%;">
                            <span class="param-label">最大tokens:</span>
                            <input type="number" class="param-input diagram-input" id="diagram-llm-max-tokens" min="100" max="8000" value="3000"/>
                        </div>
                    </div>
                </div>
                <div class="diagram-controls">
                    <div class="control-section">
                        <h4>🤖 模型選擇</h4>
                        <div class="form-group">
                            <label for="diagram-rerank-model">Reranker 模型:</label>
                            <select id="diagram-rerank-model" class="param-input">
                                <option value="onnx_int8">ONNX INT8 (推薦)</option>
                                <option value="onnx_fp32">ONNX FP32</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="diagram-llm-model">LLM 模型:</label>
                            <select id="diagram-llm-model" class="param-input">
                                <option value="gemini-2.0-flash">Gemini 2.0 Flash</option>
                                <option value="gemini-2.5-flash">Gemini 2.5 Flash</option>
                            </select>
                        </div>
                    </div>
                    <div class="control-section">
                        <h4>⚙️ 系統設置</h4>
                        <div class="form-group">
                            <label for="diagram-enable-rerank">啟用重排序:</label>
                            <input type="checkbox" id="diagram-enable-rerank" checked>
                        </div>
                        <div class="form-group">
                            <label for="diagram-enable-bm25">啟用 BM25:</label>
                            <input type="checkbox" id="diagram-enable-bm25" checked>
                        </div>
                    </div>
                </div>
                <div class="form-actions">
                    <button id="apply-diagram-params-btn" class="btn btn-primary">🚀 應用參數</button>
                    <button id="reset-diagram-params-btn" class="btn btn-secondary">🔄 重置默認</button>
                </div>
                <div id="diagram-status" class="status-message"></div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>


