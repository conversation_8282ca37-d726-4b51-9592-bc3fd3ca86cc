"""
FastAPI Web Interface 應用
提供靜態文件服務和基本的 Web 界面功能
"""

from fastapi import FastAPI, Request, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.middleware.httpsredirect import HTTPSRedirectMiddleware
from pydantic import BaseModel
import os
import httpx
import json
from pathlib import Path
from dotenv import load_dotenv
from typing import Dict, Any, Optional

# 載入環境變數
load_dotenv()

# 請求模型
class QueryRequest(BaseModel):
    query_text: str
    include_llm_response: Optional[bool] = False
    raw_documents_only: Optional[bool] = False
    config_override: Optional[Dict[str, Any]] = None

class ProxyRequest(BaseModel):
    url: str
    method: str = "GET"
    headers: Optional[Dict[str, str]] = None
    body: Optional[Dict[str, Any]] = None

# 創建 FastAPI 應用
app = FastAPI(
    title="RAG Web Interface",
    description="RAG 智能查詢系統 Web 界面",
    version="1.0.0",
    docs_url=None,  # 生產環境關閉 API 文檔
    redoc_url=None  # 生產環境關閉 ReDoc
)

# 安全中間件配置
# 1. HTTPS 重定向 (Cloud Run 自動處理，但保留配置)
if os.getenv("ENVIRONMENT") == "production":
    app.add_middleware(HTTPSRedirectMiddleware)

# 2. 信任的主機中間件
allowed_hosts = os.getenv("ALLOWED_HOSTS", "*").split(",")
if allowed_hosts != ["*"]:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=allowed_hosts
    )

# 3. CORS 中間件 - 安全配置
api_base_url = os.getenv("API_BASE_URL", "http://localhost:8001")
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        api_base_url,  # 允許來自 API 服務的請求
        "https://*.a.run.app",  # 允許來自 Cloud Run 的請求
    ],
    allow_credentials=False,  # 不允許憑證
    allow_methods=["GET", "POST", "OPTIONS"],  # 限制允許的方法
    allow_headers=[
        "Accept",
        "Accept-Language",
        "Content-Language",
        "Content-Type",
        "Authorization"
    ],
)

# 4. 安全標頭中間件
@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    response = await call_next(request)

    # 安全標頭
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    response.headers["Content-Security-Policy"] = (
        "default-src 'self'; "
        "script-src 'self' 'unsafe-inline' https://fonts.googleapis.com; "
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; "
        "font-src 'self' https://fonts.gstatic.com; "
        "img-src 'self' data:; "
        "connect-src 'self' " + api_base_url + ";"
    )

    return response

# 獲取當前文件目錄
current_dir = Path(__file__).parent

# 掛載靜態文件
app.mount("/static", StaticFiles(directory=current_dir), name="static")

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """
    提供主頁面
    """
    index_path = current_dir / "index.html"
    if index_path.exists():
        return FileResponse(index_path)
    else:
        return HTMLResponse("""
        <html>
            <head><title>RAG Web Interface</title></head>
            <body>
                <h1>RAG Web Interface</h1>
                <p>index.html 文件未找到</p>
            </body>
        </html>
        """)

@app.get("/health")
async def health_check():
    """
    健康檢查端點
    """
    return {
        "status": "healthy",
        "service": "RAG Web Interface",
        "version": "1.0.0"
    }

@app.post("/api/proxy")
async def api_proxy(request: ProxyRequest):
    """
    API 代理端點 - 解決 Cloud Run 到 VM 的連接問題
    """
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            # 準備請求參數
            kwargs = {
                "method": request.method,
                "url": request.url,
                "headers": request.headers or {}
            }

            # 如果有請求體，添加 JSON 數據
            if request.body:
                kwargs["json"] = request.body

            # 發送請求
            response = await client.request(**kwargs)

            # 返回響應
            return {
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "data": response.json() if response.headers.get("content-type", "").startswith("application/json") else response.text
            }

    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="請求超時 - VM API 響應太慢")
    except httpx.ConnectError:
        raise HTTPException(status_code=503, detail="連接失敗 - 無法連接到 VM API")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"代理請求失敗: {str(e)}")

@app.post("/api/query")
async def query_proxy(request: QueryRequest):
    """
    RAG 查詢代理端點
    """
    vm_api_url = os.getenv("VM_API_URL", "http://34.46.143.75:8001")

    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                f"{vm_api_url}/query",
                json=request.model_dump(),
                headers={"Content-Type": "application/json"}
            )

            if response.status_code == 200:
                return response.json()
            else:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"VM API 錯誤: {response.text}"
                )

    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="查詢超時 - VM API 響應太慢")
    except httpx.ConnectError:
        raise HTTPException(status_code=503, detail="連接失敗 - 無法連接到 VM API")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查詢失敗: {str(e)}")

@app.get("/api/config")
async def config_proxy():
    """
    配置信息代理端點
    """
    vm_api_url = os.getenv("VM_API_URL", "http://34.46.143.75:8001")

    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(f"{vm_api_url}/config")

            if response.status_code == 200:
                return response.json()
            else:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"VM API 錯誤: {response.text}"
                )

    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="請求超時")
    except httpx.ConnectError:
        raise HTTPException(status_code=503, detail="連接失敗")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"獲取配置失敗: {str(e)}")

@app.post("/api/config")
async def update_config_proxy(config_data: Dict[str, Any]):
    """
    更新配置代理端點
    """
    vm_api_url = os.getenv("VM_API_URL", "http://34.46.143.75:8001")

    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{vm_api_url}/config",
                json=config_data,
                headers={"Content-Type": "application/json"}
            )

            if response.status_code == 200:
                return response.json()
            else:
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"VM API 錯誤: {response.text}"
                )

    except httpx.TimeoutException:
        raise HTTPException(status_code=504, detail="更新配置超時")
    except httpx.ConnectError:
        raise HTTPException(status_code=503, detail="連接失敗 - 無法連接到 VM API")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新配置失敗: {str(e)}")

@app.get("/api/health")
async def vm_health_proxy():
    """
    VM 健康檢查代理端點
    """
    vm_api_url = os.getenv("VM_API_URL", "http://34.46.143.75:8001")

    try:
        async with httpx.AsyncClient(timeout=15.0) as client:
            response = await client.get(f"{vm_api_url}/health")

            return {
                "vm_status": "healthy" if response.status_code == 200 else "unhealthy",
                "status_code": response.status_code,
                "response": response.json() if response.status_code == 200 else response.text
            }

    except Exception as e:
        return {
            "vm_status": "unreachable",
            "error": str(e)
        }

@app.get("/favicon.ico")
async def favicon():
    """
    提供 favicon
    """
    favicon_path = current_dir / "favicon.ico"
    if favicon_path.exists():
        return FileResponse(favicon_path)
    else:
        # 返回空響應
        return HTMLResponse("", status_code=204)

# 處理所有其他路由，返回 index.html (SPA 支持)
@app.get("/{full_path:path}")
async def catch_all(full_path: str):
    """
    捕獲所有路由，支持 SPA 路由
    """
    # 如果是靜態文件請求，嘗試直接返回文件
    file_path = current_dir / full_path
    if file_path.exists() and file_path.is_file():
        return FileResponse(file_path)
    
    # 否則返回 index.html
    index_path = current_dir / "index.html"
    if index_path.exists():
        return FileResponse(index_path)
    else:
        return HTMLResponse("Page not found", status_code=404)

if __name__ == "__main__":
    import uvicorn
    
    # 獲取端口（Cloud Run 使用環境變量 PORT）
    port = int(os.environ.get("PORT", 8080))
    
    uvicorn.run(
        "fastapi_app:app",
        host="0.0.0.0",
        port=port,
        reload=False  # 生產環境關閉 reload
    )
