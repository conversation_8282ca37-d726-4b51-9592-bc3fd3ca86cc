# 穩定版 Dockerfile - 解決 SentencePiece 依賴問題

# 使用多階段構建來優化最終映像大小
FROM python:3.11-slim as builder

# 設置工作目錄
WORKDIR /app

# 安裝構建依賴
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    gcc \
    g++ \
    python3-dev \
    cmake \
    pkg-config \
    git \
    && rm -rf /var/lib/apt/lists/*

# 安裝 Poetry
RUN pip install --no-cache-dir "poetry==1.8.3"

# 配置 Poetry
ENV POETRY_VIRTUALENVS_IN_PROJECT=true
ENV POETRY_VIRTUALENVS_PATH="/app/.venv"

# 複製項目配置文件
COPY pyproject.toml poetry.lock ./

# 複製源代碼
COPY rag_query_tool ./rag_query_tool/
COPY .env* ./

# 先安裝關鍵依賴
RUN pip install --no-cache-dir \
    sentencepiece \
    transformers \
    torch --index-url https://download.pytorch.org/whl/cpu

# 安裝項目依賴
RUN poetry install --no-root --only=main

# === 運行階段 ===
FROM python:3.11-slim

# 設置工作目錄
WORKDIR /app

# 安裝運行時依賴
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libpq5 \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# 從構建階段複製虛擬環境
COPY --from=builder /app/.venv /app/.venv

# 複製應用代碼
COPY --from=builder /app/rag_query_tool /app/rag_query_tool
COPY --from=builder /app/.env* /app/

# 確保虛擬環境在 PATH 中
ENV PATH="/app/.venv/bin:$PATH"

# 創建非 root 用戶
RUN useradd --create-home --shell /bin/bash app && \
    chown -R app:app /app
USER app

# 暴露端口
EXPOSE 8001

# 健康檢查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# 啟動命令
CMD ["uvicorn", "rag_query_tool.api:app", "--host", "0.0.0.0", "--port", "8001"]
