# 項目結構說明

## 📁 核心文件

### 根目錄
```
├── Dockerfile              # Docker 容器配置
├── pyproject.toml          # Poetry 依賴管理
├── poetry.lock             # 鎖定的依賴版本
├── .env.example            # 環境變數範例
├── .dockerignore           # Docker 構建忽略文件
└── README.md               # 項目說明
```

### 主要應用
```
rag_query_tool/
├── api.py                  # FastAPI 主應用
├── query_tool.py           # RAG 查詢核心邏輯
├── config/                 # 配置管理
├── database/               # 數據庫模型和連接
├── embeddings/             # 嵌入模型和向量處理
├── llm/                    # LLM 生成器
├── retrieval/              # 檢索組件 (BM25, 重排序)
└── utils/                  # 工具函數
```

### Web 界面 (獨立部署)
```
web_interface/
├── index.html              # 主頁面
├── script.js               # 前端邏輯
├── styles.css              # 樣式文件
├── Dockerfile              # Web 界面 Docker 配置
└── requirements.txt        # Python 依賴 (如果使用 FastAPI 後端)
```

## 📚 文檔

- `API使用說明書.md` - 詳細的 API 使用指南
- `API_使用範例.md` - API 調用範例
- `VM_部署指南.md` - VM 部署步驟
- `Cloud_Run_部署指南.md` - Cloud Run 部署指南
- `專案報告.md` - 項目技術報告

## 🚀 部署文件

- `cloudbuild.yaml` - Google Cloud Build 配置
- `web_interface/cloudbuild.yaml` - Web 界面 Cloud Build 配置

## 🧹 已清理的文件

以下腳本文件已被移除以保持項目乾淨：
- `*.sh` - 所有 shell 腳本
- `test_*.py` - 測試腳本
- 部署相關的臨時腳本

## 💡 使用建議

1. **手動構建**: 使用 `docker build -t rag-api-app .`
2. **環境配置**: 複製 `.env.example` 到 `.env` 並修改配置
3. **獨立部署**: API 和 Web 界面分別部署
4. **文檔參考**: 查看相應的部署指南進行部署

## 🔧 開發流程

1. 修改代碼
2. 手動測試 API
3. 手動構建 Docker 映像
4. 部署到目標環境
5. 驗證功能正常
