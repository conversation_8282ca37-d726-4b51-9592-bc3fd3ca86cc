# 这是 Poetry 的主配置节
[tool.poetry]
name = "rag-api"
version = "0.1.0"
description = ""
authors = ["Xienlu <<EMAIL>>"]
readme = "README.md"

# === 关键修正：确保 'packages' 字段位于 [tool.poetry] 下，与 name, version 等字段同级 ===
# 它绝对不能被缩进，也不能在 [tool.poetry.dependencies] 节的下面。
packages = [
    { include = "rag_query_tool" },
]
# ========================================================================================

# 这是你的项目依赖项的配置节
[tool.poetry.dependencies]
# Python 版本也放在这里
python = ">=3.10,<3.12"

# 下面是你的所有依赖项，它们现在是键值对的形式
SQLAlchemy = ">=2.0.41,<3.0.0"
dotenv = ">=0.9.9,<0.10.0"
pgvector = ">=0.4.1,<0.5.0"
sentence-transformers = ">=5.0.0,<6.0.0"
transformers = ">=4.30.0,<5.0.0"
sentencepiece = ">=0.1.99,<0.2.0"
rank-bm25 = ">=0.2.2,<0.3.0"
jieba = ">=0.42.1,<0.43.0"
google-generativeai = ">=0.8.5,<0.9.0"
psycopg2 = ">=2.9.10,<3.0.0"
fastapi = ">=0.100.0,<0.116.0"
uvicorn = {extras = ["standard"], version = ">=0.20.0,<0.36.0"}
onnx = ">=1.15.0,<2.0.0"
onnxruntime-gpu = ">=1.15.0,<2.0.0"
python-multipart = ">=0.0.6,<0.1.0"
httpx = ">=0.24.0,<0.28.0"
python-dotenv = ">=1.0.0,<2.0.0"

# 构建系统要求，通常由 Poetry 自动管理
[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"