# 🚀 RAG FastAPI Web Interface Cloud Run 部署指南

## 📋 快速部署步驟

**重要說明**：本指南使用 FastAPI 來服務您的 Web 界面，提供更好的擴展性和未來功能支持。

### 1. 準備工作

#### 安裝 Google Cloud CLI
```bash
# 下載並安裝 Google Cloud CLI
# https://cloud.google.com/sdk/docs/install

# 驗證安裝
gcloud --version
```

#### 登入並設置項目
```bash
# 登入 Google Cloud
gcloud auth login

# 列出可用項目
gcloud projects list

# 設置項目 (替換為您的項目 ID)
gcloud config set project YOUR_PROJECT_ID
```

### 2. 修改配置文件

編輯 `web_interface/deploy.sh`，修改第 8 行：
```bash
PROJECT_ID="your-actual-project-id"  # 替換為您的實際項目 ID
```

### 3. 執行部署

```bash
# 進入 web_interface 目錄
cd web_interface

# 給部署腳本執行權限 (Linux/Mac)
chmod +x deploy.sh

# 執行部署
./deploy.sh
```

**Windows PowerShell 用戶：**
```powershell
# 進入 web_interface 目錄
cd web_interface

# 手動執行部署命令
gcloud config set project YOUR_PROJECT_ID
gcloud services enable cloudbuild.googleapis.com run.googleapis.com containerregistry.googleapis.com
gcloud builds submit --config cloudbuild.yaml .
```

### 4. 獲取部署 URL

部署完成後，您會看到類似輸出：
```
✅ 部署完成！
📋 部署信息：
   服務名稱: rag-web-interface
   區域: asia-east1
   URL: https://rag-web-interface-xxx-xx.a.run.app
```

---

## 🔧 手動部署步驟 (詳細版)

如果自動腳本有問題，可以手動執行：

### 1. 啟用必要的 API
```bash
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
```

### 2. 構建 Docker 映像
```bash
# 在 web_interface 目錄下執行
gcloud builds submit --tag gcr.io/YOUR_PROJECT_ID/rag-web-interface .
```

### 3. 部署到 Cloud Run
```bash
gcloud run deploy rag-web-interface \
  --image gcr.io/YOUR_PROJECT_ID/rag-web-interface \
  --region asia-east1 \
  --platform managed \
  --allow-unauthenticated \
  --port 8080 \
  --memory 512Mi \
  --cpu 1 \
  --max-instances 10
```

---

## ⚙️ 部署後配置

### 1. 訪問您的網站
打開部署完成後提供的 URL，例如：
`https://rag-web-interface-xxx-xx.a.run.app`

### 2. 配置 API 端點
1. 點擊右上角的齒輪圖標 ⚙️
2. 設置您的後端 API 地址：
   - **文件 API 地址**：`https://your-backend-api.a.run.app`
   - **查詢 API 地址**：`https://your-backend-api.a.run.app/query`
3. 點擊「保存設置」

### 3. 測試功能
- 上傳測試文件
- 執行查詢測試
- 檢查 API 連接狀態

---

## 🔍 常見問題排查

### 問題 1: 權限錯誤
```bash
# 確保已登入並設置正確項目
gcloud auth login
gcloud config set project YOUR_PROJECT_ID
```

### 問題 2: API 未啟用
```bash
# 手動啟用所需 API
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
```

### 問題 3: 構建失敗
```bash
# 檢查 Dockerfile 是否存在
ls -la Dockerfile

# 檢查當前目錄
pwd
# 應該在 web_interface 目錄下
```

### 問題 4: 部署後無法訪問
1. 檢查 Cloud Run 服務是否允許未經身份驗證的調用
2. 確認端口設置為 8080
3. 檢查防火牆設置

---

## 📊 部署配置說明

### Dockerfile 配置
- **基礎映像**：python:3.11-slim (FastAPI 支持)
- **端口**：8080 (Cloud Run 要求)
- **功能**：FastAPI 服務、靜態文件服務、API 端點支持

### Cloud Run 配置
- **區域**：asia-east1 (亞洲東部)
- **記憶體**：512Mi
- **CPU**：1 核心
- **最大實例數**：10
- **最小實例數**：0 (節省成本)

### 安全設置
- **HTTPS**：自動啟用
- **安全標頭**：X-Frame-Options, X-XSS-Protection 等
- **CORS**：需要在後端 API 配置

---

## 💰 成本估算

### Cloud Run 定價 (asia-east1)
- **CPU**：$0.00002400 每 vCPU-秒
- **記憶體**：$0.00000250 每 GiB-秒
- **請求**：$0.40 每百萬請求

### 估算成本 (月使用量)
- **輕度使用** (1000 次訪問/月)：約 $0.50
- **中度使用** (10000 次訪問/月)：約 $2.00
- **重度使用** (100000 次訪問/月)：約 $15.00

*實際成本可能因使用模式而異*

---

## 🔄 更新部署

### 方法 1: 重新執行部署腳本
```bash
cd web_interface
./deploy.sh
```

### 方法 2: 使用 Cloud Build
```bash
gcloud builds submit --config cloudbuild.yaml .
```

### 方法 3: 直接更新服務
```bash
# 構建新映像
gcloud builds submit --tag gcr.io/YOUR_PROJECT_ID/rag-web-interface .

# 更新服務
gcloud run services update rag-web-interface \
  --image gcr.io/YOUR_PROJECT_ID/rag-web-interface \
  --region asia-east1
```

---

## 📱 監控和日誌

### 查看服務狀態
```bash
gcloud run services describe rag-web-interface --region asia-east1
```

### 查看日誌
```bash
gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name=rag-web-interface" --limit 50
```

### 監控面板
訪問 [Google Cloud Console](https://console.cloud.google.com/run) 查看：
- 請求量統計
- 錯誤率
- 響應時間
- 資源使用情況

---

## 🎯 下一步建議

### 1. 設置自定義域名
```bash
gcloud run domain-mappings create \
  --service rag-web-interface \
  --domain your-domain.com \
  --region asia-east1
```

### 2. 配置 CI/CD
- 設置 GitHub Actions 自動部署
- 配置分支保護規則
- 實現自動化測試

### 3. 性能優化
- 啟用 CDN
- 優化圖片和資源
- 實現服務端緩存

### 4. 安全加固
- 添加身份驗證
- 配置 WAF
- 實現 API 限流

---

## 📞 技術支持

### 官方文檔
- [Cloud Run 文檔](https://cloud.google.com/run/docs)
- [Cloud Build 文檔](https://cloud.google.com/build/docs)

### 常用命令
```bash
# 查看所有 Cloud Run 服務
gcloud run services list

# 刪除服務
gcloud run services delete rag-web-interface --region asia-east1

# 查看服務 URL
gcloud run services describe rag-web-interface --region asia-east1 --format='value(status.url)'
```

---

*部署成功後，您的 RAG Web Interface 將可以通過 HTTPS 在全球範圍內訪問！*
