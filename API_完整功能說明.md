# RAG Query Tool - 完整 API 功能說明

## 📋 目錄

1. [API 概述](#api-概述)
2. [基本信息](#基本信息)
3. [認證與安全](#認證與安全)
4. [端點詳細說明](#端點詳細說明)
5. [請求與響應格式](#請求與響應格式)
6. [錯誤處理](#錯誤處理)
7. [使用範例](#使用範例)
8. [性能優化](#性能優化)
9. [部署配置](#部署配置)

---

## API 概述

RAG Query Tool 是一個基於 FastAPI 的智能檢索增強生成系統，整合了多種先進的檢索和生成技術：

### 🔍 核心技術棧
- **混合檢索**: 向量相似度 + BM25 稀疏檢索
- **智能融合**: Reciprocal Rank Fusion (RRF) 算法
- **重排序**: ONNX 優化的重排序模型
- **LLM 生成**: Google Gemini API 集成
- **異步處理**: 高性能並發查詢

### 🎯 主要功能
- 智能文檔檢索與排序
- 多模態內容處理（文本 + 圖像）
- 可配置的查詢參數
- 詳細的處理統計信息
- 靈活的輸出格式

---

## 基本信息

### 服務端點
- **本地開發**: `http://localhost:8001`
- **生產環境**: `http://your-vm-ip:8001`
- **API 文檔**: `/docs` (Swagger UI)
- **ReDoc 文檔**: `/redoc`

### 支持的 HTTP 方法
- `GET` - 獲取配置和狀態信息
- `POST` - 執行查詢操作

### 內容類型
- **請求**: `application/json`
- **響應**: `application/json`

---

## 認證與安全

### CORS 配置
```json
{
  "allowed_origins": ["*"],
  "allowed_methods": ["GET", "POST", "OPTIONS"],
  "allowed_headers": ["*"]
}
```

### 安全標頭
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`

---

## 端點詳細說明

### 1. 健康檢查端點

#### `GET /health`

**描述**: 檢查 API 服務狀態

**請求參數**: 無

**響應格式**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0",
  "services": {
    "database": "connected",
    "embedding_model": "loaded",
    "llm_service": "available",
    "reranker": "loaded"
  }
}
```

**狀態碼**:
- `200` - 服務正常
- `503` - 服務不可用

---

### 2. 配置信息端點

#### `GET /config`

**描述**: 獲取當前 API 配置參數

**請求參數**: 無

**響應格式**:
```json
{
  "vector_top_k": 10,
  "bm25_top_k": 10,
  "rrf_k": 60,
  "rrf_top_k": 15,
  "rerank_top_k": 5,
  "enable_rerank": true,
  "llm_temperature": 0.7,
  "llm_max_tokens": 1000,
  "llm_top_p": 0.9,
  "max_context_length": 4000,
  "model_info": {
    "embedding_model": "bge-base-zh-v1.5",
    "rerank_model": "BAAI/bge-reranker-base",
    "llm_model": "gemini-pro"
  }
}
```

---

### 3. 主查詢端點

#### `POST /query`

**描述**: 執行 RAG 查詢，支持多種輸出模式

**請求格式**:
```json
{
  "query_text": "string",
  "include_llm_response": boolean,
  "raw_documents_only": boolean,
  "config_override": {
    "vector_top_k": integer,
    "bm25_top_k": integer,
    "rrf_k": integer,
    "rrf_top_k": integer,
    "rerank_top_k": integer,
    "enable_rerank": boolean,
    "llm_temperature": float,
    "llm_max_tokens": integer,
    "llm_top_p": float
  }
}
```

**參數說明**:

| 參數 | 類型 | 必填 | 默認值 | 說明 |
|------|------|------|--------|------|
| `query_text` | string | ✅ | - | 查詢文本 |
| `include_llm_response` | boolean | ❌ | `false` | 是否包含 LLM 生成的回答 |
| `raw_documents_only` | boolean | ❌ | `false` | 是否只返回原始文檔 |
| `config_override` | object | ❌ | `{}` | 覆蓋默認配置參數 |

**配置參數詳解**:

| 配置項 | 類型 | 範圍 | 說明 |
|--------|------|------|------|
| `vector_top_k` | integer | 1-50 | 向量檢索返回的文檔數量 |
| `bm25_top_k` | integer | 1-50 | BM25 檢索返回的文檔數量 |
| `rrf_k` | integer | 1-100 | RRF 融合參數 |
| `rrf_top_k` | integer | 1-30 | RRF 融合後保留的文檔數量 |
| `rerank_top_k` | integer | 1-10 | 重排序後返回的文檔數量 |
| `enable_rerank` | boolean | - | 是否啟用重排序 |
| `llm_temperature` | float | 0.0-2.0 | LLM 生成溫度 |
| `llm_max_tokens` | integer | 1-4000 | LLM 最大生成 token 數 |
| `llm_top_p` | float | 0.0-1.0 | LLM 核心採樣參數 |

---

## 請求與響應格式

### 響應模式

#### 模式 1: 完整 RAG 響應 (默認)
```json
{
  "response": "LLM 生成的完整回答",
  "documents": [
    {
      "content": "文檔內容",
      "source": "文檔來源",
      "score": 0.95,
      "metadata": {
        "vector_rank": 1,
        "bm25_rank": 2,
        "fused_score": 0.0147,
        "rerank_score": -5.40,
        "parent_document_id": "doc_123",
        "document_metadata": {}
      }
    }
  ],
  "processing_info": {
    "total_time": 7.52,
    "vector_search_time": 1.76,
    "bm25_search_time": 0.002,
    "rrf_fusion_time": 0.0005,
    "rerank_time": 2.06,
    "llm_generation_time": 3.71,
    "vector_results_count": 10,
    "bm25_results_count": 10,
    "rrf_results_count": 15,
    "final_results_count": 5
  }
}
```

#### 模式 2: 只返回 LLM 回答
```json
{
  "response": "LLM 生成的回答文本"
}
```

#### 模式 3: 只返回原始文檔
```json
{
  "documents": [
    {
      "content": "文檔內容",
      "source": "文檔來源", 
      "score": 0.95,
      "metadata": {
        "vector_rank": 1,
        "bm25_rank": 2,
        "fused_score": 0.0147,
        "rerank_score": -5.40,
        "parent_document_id": "doc_123"
      }
    }
  ],
  "processing_info": {
    "total_time": 3.81,
    "vector_search_time": 1.76,
    "bm25_search_time": 0.002,
    "rrf_fusion_time": 0.0005,
    "rerank_time": 2.06,
    "vector_results_count": 10,
    "bm25_results_count": 10,
    "rrf_results_count": 15,
    "final_results_count": 5
  }
}
```

### 文檔元數據說明

| 字段 | 類型 | 說明 |
|------|------|------|
| `vector_rank` | integer | 向量檢索中的排名 |
| `bm25_rank` | integer | BM25 檢索中的排名 |
| `fused_score` | float | RRF 融合後的分數 |
| `rerank_score` | float | 重排序模型的分數 |
| `parent_document_id` | string | 父文檔 ID |
| `document_metadata` | object | 額外的文檔元數據 |

---

## 錯誤處理

### 錯誤響應格式
```json
{
  "detail": "錯誤描述",
  "error_code": "ERROR_CODE",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### 常見錯誤碼

| 狀態碼 | 錯誤碼 | 說明 | 解決方案 |
|--------|--------|------|----------|
| 400 | `INVALID_REQUEST` | 請求參數無效 | 檢查請求格式和參數 |
| 422 | `VALIDATION_ERROR` | 數據驗證失敗 | 檢查參數類型和範圍 |
| 500 | `INTERNAL_ERROR` | 服務器內部錯誤 | 聯繫技術支持 |
| 503 | `SERVICE_UNAVAILABLE` | 服務不可用 | 檢查服務狀態 |
| 504 | `TIMEOUT_ERROR` | 請求超時 | 減少查詢複雜度或重試 |

### 具體錯誤情況

#### 1. 查詢文本為空
```json
{
  "detail": "查詢文本不能為空",
  "error_code": "EMPTY_QUERY"
}
```

#### 2. 配置參數超出範圍
```json
{
  "detail": "vector_top_k 必須在 1-50 之間",
  "error_code": "INVALID_PARAMETER"
}
```

#### 3. 數據庫連接失敗
```json
{
  "detail": "數據庫連接失敗",
  "error_code": "DATABASE_ERROR"
}
```

#### 4. LLM 服務不可用
```json
{
  "detail": "LLM 服務暫時不可用",
  "error_code": "LLM_SERVICE_ERROR"
}
```

---

## 使用範例

### 範例 1: 基本查詢
```bash
curl -X POST "http://localhost:8001/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query_text": "什麼是貓咪的基本特徵？"
  }'
```

### 範例 2: 包含 LLM 回答的查詢
```bash
curl -X POST "http://localhost:8001/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query_text": "如何照顧幼貓？",
    "include_llm_response": true
  }'
```

### 範例 3: 只獲取原始文檔
```bash
curl -X POST "http://localhost:8001/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query_text": "貓咪疫苗接種",
    "raw_documents_only": true
  }'
```

### 範例 4: 自定義配置參數
```bash
curl -X POST "http://localhost:8001/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query_text": "貓咪營養需求",
    "include_llm_response": true,
    "config_override": {
      "vector_top_k": 15,
      "rerank_top_k": 3,
      "llm_temperature": 0.5,
      "enable_rerank": true
    }
  }'
```

### 範例 5: 禁用重排序的快速查詢
```bash
curl -X POST "http://localhost:8001/query" \
  -H "Content-Type: application/json" \
  -d '{
    "query_text": "貓咪行為問題",
    "raw_documents_only": true,
    "config_override": {
      "enable_rerank": false,
      "vector_top_k": 5,
      "bm25_top_k": 5
    }
  }'
```

---

## 性能優化

### 查詢性能建議

#### 1. 快速查詢模式
```json
{
  "query_text": "your_query",
  "raw_documents_only": true,
  "config_override": {
    "enable_rerank": false,
    "vector_top_k": 5,
    "bm25_top_k": 5
  }
}
```

#### 2. 高精度查詢模式
```json
{
  "query_text": "your_query",
  "include_llm_response": true,
  "config_override": {
    "enable_rerank": true,
    "vector_top_k": 20,
    "bm25_top_k": 20,
    "rerank_top_k": 5
  }
}
```

#### 3. 平衡模式（推薦）
```json
{
  "query_text": "your_query",
  "include_llm_response": true,
  "config_override": {
    "enable_rerank": true,
    "vector_top_k": 10,
    "bm25_top_k": 10,
    "rerank_top_k": 3
  }
}
```

### 性能指標參考

| 模式 | 平均響應時間 | 精確度 | 適用場景 |
|------|-------------|--------|----------|
| 快速模式 | 1-2 秒 | 中等 | 實時搜索、預覽 |
| 平衡模式 | 3-5 秒 | 高 | 一般查詢 |
| 高精度模式 | 5-8 秒 | 最高 | 重要查詢、研究 |

---

## 部署配置

### 環境變數

```env
# 數據庫配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=rag_database
DB_USER=postgres
DB_PASSWORD=your_password

# API 配置
GEMINI_API_KEY=your_gemini_api_key
ENVIRONMENT=production
ALLOWED_ORIGINS=*

# 模型路徑
RERANK_ONNX_PATH=rag_query_tool/embeddings/BAAI--bge-reranker-base/onnx_model/model.int8.onnx
LOCAL_EMBEDDING_MODEL_PATH=rag_query_tool/embeddings/bge-base-zh-v1.5
```

### Docker 部署

```bash
# 構建映像
docker build -t rag-api-app .

# 運行容器
docker run -d \
  -p 8001:8001 \
  --name rag-api-V1 \
  --env-file .env \
  rag-api-app

# 健康檢查
curl http://localhost:8001/health
```

### 監控和日誌

#### 健康檢查端點
- **URL**: `/health`
- **頻率**: 每 30 秒
- **超時**: 30 秒

#### 日誌級別
- `INFO` - 正常操作日誌
- `WARNING` - 性能警告
- `ERROR` - 錯誤和異常
- `DEBUG` - 詳細調試信息

#### 關鍵指標監控
- 響應時間
- 查詢成功率
- 錯誤率
- 並發連接數
- 資源使用率

---

## 版本信息

- **API 版本**: v1.0.0
- **FastAPI 版本**: 0.100+
- **Python 版本**: 3.11+
- **更新日期**: 2024-01-15

---

## 技術支持

如有問題或需要技術支持，請參考：

1. **API 文檔**: `/docs` 端點
2. **健康檢查**: `/health` 端點
3. **配置信息**: `/config` 端點
4. **錯誤日誌**: 檢查容器日誌

---

*本文檔涵蓋了 RAG Query Tool API 的所有功能和使用方法。如有更新，請參考最新版本的文檔。*
