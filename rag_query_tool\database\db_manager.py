"""
db_manager.py

此模組提供了資料庫連接管理和數據操作的工具。
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import OperationalError
from typing import List, Optional
import uuid

from dotenv import load_dotenv
import os
load_dotenv(os.path.join(os.path.dirname(__file__), '..', '.env')) # 加載 .env 檔案中的環境變數

DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = int(os.getenv("DB_PORT", "5432"))
DB_NAME = os.getenv("DB_NAME", "rag_ocr_db")
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASSWORD = os.getenv("DB_PASSWORD", "rag") # 修改導入路徑
from rag_query_tool.utils.document_model import Document # 修改導入路徑
from rag_query_tool.database.models import Base, ParentDocument, ChildDocument, create_all_tables # 修改導入路徑

class DBManager:
    """
    資料庫管理類，負責連接、表創建和數據操作。
    """
    def __init__(self, engine=None):
        """
        初始化 DBManager，建立資料庫連接引擎。
        Args:
            engine (Optional[Engine]): 可選的 SQLAlchemy 引擎實例。如果提供，則使用此引擎；
                                       否則，根據 settings.py 中的配置創建一個新引擎。
        """
        if engine:
            self.engine = engine
        else:
            self.DATABASE_URL = f"postgresql+psycopg2://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
            self.engine = create_engine(self.DATABASE_URL)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)

    def get_db(self):
        """
        獲取資料庫會話。
        """
        db = self.SessionLocal()
        try:
            yield db
        finally:
            db.close()

    def create_tables(self):
        """
        創建所有定義的資料庫表。
        """
        try:
            create_all_tables(self.engine)
            print("資料庫表已成功創建或已存在。")
        except OperationalError as e:
            print(f"連接資料庫失敗或創建表時發生錯誤: {e}")
            print("請確保 PostgreSQL 服務正在運行，並且 pgvector 擴展已啟用。")
        except Exception as e:
            print(f"創建表時發生未知錯誤: {e}")

    def insert_parent_document(self, doc: Document) -> uuid.UUID:
        """
        將一個 ParentDocument 存入資料庫。

        Args:
            doc (Document): 待儲存的 Document 物件。

        Returns:
            uuid.UUID: 插入的 ParentDocument 的 ID。
        """
        db: Session = self.SessionLocal()
        try:
            parent_doc = ParentDocument(
                file_path=doc.metadata.get("source", "N/A"),
                title=doc.metadata.get("title"),
                page_content=doc.page_content,
                document_metadata=doc.metadata
            )
            db.add(parent_doc)
            db.commit()
            db.refresh(parent_doc)
            print(f"Parent Document '{parent_doc.title[:30] if parent_doc.title else parent_doc.page_content[:30]}...' (ID: {parent_doc.id}) 已儲存。")
            return parent_doc.id
        except Exception as e:
            db.rollback()
            print(f"儲存 Parent Document 失敗: {e}")
            raise
        finally:
            db.close()

    def get_parent_document_by_id(self, parent_id: uuid.UUID) -> Optional[ParentDocument]:
        """
        根據 ID 獲取 ParentDocument。

        Args:
            parent_id (uuid.UUID): ParentDocument 的 ID。

        Returns:
            Optional[ParentDocument]: 找到的 ParentDocument 物件，如果未找到則為 None。
        """
        db: Session = self.SessionLocal()
        try:
            return db.query(ParentDocument).filter(ParentDocument.id == parent_id).first()
        except Exception as e:
            print(f"根據 ID 獲取 Parent Document 失敗: {e}")
            raise
        finally:
            db.close()

    def insert_child_document(self, parent_id: uuid.UUID, doc: Document, embedding: List[float]):
        """
        將一個 ChildDocument 及其向量嵌入存入資料庫。

        Args:
            parent_id (uuid.UUID): 關聯的 ParentDocument 的 ID。
            doc (Document): 待儲存的 Document 物件。
            embedding (List[float]): 文本的向量嵌入。
        """
        db: Session = self.SessionLocal()
        try:
            child_doc = ChildDocument(
                parent_id=parent_id,
                page_content=doc.page_content,
                embedding=embedding,
                document_metadata=doc.metadata
            )
            db.add(child_doc)
            db.commit()
            db.refresh(child_doc)
            print(f"Child Document '{child_doc.page_content[:30]}...' (ID: {child_doc.id}) 已儲存，關聯 Parent ID: {parent_id}。")
        except Exception as e:
            db.rollback()
            print(f"儲存 Child Document 失敗: {e}")
            raise
        finally:
            db.close()

    def query_child_documents_by_vector_similarity(self, query_embedding: List[float], top_k: int = 5) -> List[ChildDocument]:
        """
        根據向量相似度查詢最相關的 ChildDocument。

        Args:
            query_embedding (List[float]): 查詢文本的向量嵌入。
            top_k (int): 返回最相關文檔的數量。

        Returns:
            List[ChildDocument]: 最相關的 ChildDocument 列表。
        """
        db: Session = self.SessionLocal()
        try:
            # 使用 pgvector 的向量相似度操作符 (<=>) 進行查詢
            # order_by 越小表示越相似 (距離越近)
            results = db.query(ChildDocument).order_by(ChildDocument.embedding.cosine_distance(query_embedding)).limit(top_k).all()
            return results
        except Exception as e:
            print(f"向量相似度查詢失敗: {e}")
            raise
        finally:
            db.close()

    def get_all_child_documents(self) -> List[ChildDocument]:
        """
        從資料庫中獲取所有 ChildDocument。

        Returns:
            List[ChildDocument]: 所有 ChildDocument 物件的列表。
        """
        db: Session = self.SessionLocal()
        try:
            results = db.query(ChildDocument).all()
            return results
        except Exception as e:
            print(f"獲取所有 ChildDocument 失敗: {e}")
            raise
        finally:
            db.close()