#!/bin/bash

# VM 連接診斷腳本
# 在 VM 上執行此腳本來診斷連接問題

echo "🔍 開始診斷 VM API 連接問題..."
echo "=================================="

# 1. 檢查 Docker 容器狀態
echo "1. 檢查 Docker 容器狀態:"
docker ps | grep rag-api
if [ $? -eq 0 ]; then
    echo "✅ RAG API 容器正在運行"
else
    echo "❌ RAG API 容器未運行"
    echo "嘗試啟動容器..."
    docker start rag-api-V1 2>/dev/null || echo "容器不存在，請重新部署"
fi
echo ""

# 2. 檢查端口監聽
echo "2. 檢查端口監聽狀態:"
sudo ss -tulnp | grep 8001
if [ $? -eq 0 ]; then
    echo "✅ 端口 8001 正在監聽"
else
    echo "❌ 端口 8001 未監聽"
fi
echo ""

# 3. 測試本地連接
echo "3. 測試本地 API 連接:"
curl -s -o /dev/null -w "%{http_code}" http://localhost:8001/health
if [ $? -eq 0 ]; then
    echo "✅ 本地 API 連接成功"
else
    echo "❌ 本地 API 連接失敗"
fi
echo ""

# 4. 檢查外部 IP
echo "4. 檢查 VM 外部 IP:"
EXTERNAL_IP=$(curl -s http://checkip.amazonaws.com/)
echo "外部 IP: $EXTERNAL_IP"
echo ""

# 5. 檢查防火牆規則
echo "5. 檢查 GCP 防火牆規則:"
gcloud compute firewall-rules list --filter="allowed.ports:8001" --format="table(name,allowed[].ports,sourceRanges[])"
echo ""

# 6. 檢查 VM 網路標籤
echo "6. 檢查 VM 網路標籤:"
INSTANCE_NAME=$(curl -s -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/instance/name)
ZONE=$(curl -s -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/instance/zone | cut -d'/' -f4)
echo "VM 名稱: $INSTANCE_NAME"
echo "區域: $ZONE"
gcloud compute instances describe $INSTANCE_NAME --zone=$ZONE --format='value(tags.items[])' 2>/dev/null || echo "無法獲取標籤信息"
echo ""

# 7. 測試外部連接
echo "7. 測試外部 API 連接:"
curl -s -o /dev/null -w "%{http_code}" http://$EXTERNAL_IP:8001/health
if [ $? -eq 0 ]; then
    echo "✅ 外部 API 連接成功"
else
    echo "❌ 外部 API 連接失敗"
fi
echo ""

# 8. 檢查 Docker 日誌
echo "8. 檢查 API 容器日誌 (最後 10 行):"
docker logs rag-api-V1 --tail 10 2>/dev/null || echo "無法獲取容器日誌"
echo ""

echo "=================================="
echo "🔧 建議的修復步驟:"
echo ""
echo "如果容器未運行:"
echo "  docker restart rag-api-V1"
echo ""
echo "如果防火牆規則不存在:"
echo "  gcloud compute firewall-rules create allow-rag-api-8001 \\"
echo "    --allow tcp:8001 \\"
echo "    --source-ranges 0.0.0.0/0 \\"
echo "    --description 'Allow RAG API access'"
echo ""
echo "如果需要添加網路標籤:"
echo "  gcloud compute instances add-tags $INSTANCE_NAME \\"
echo "    --zone=$ZONE \\"
echo "    --tags=rag-api"
echo ""
echo "測試外部連接:"
echo "  curl http://$EXTERNAL_IP:8001/health"
echo ""
echo "在 Web 界面中使用的 URL:"
echo "  http://$EXTERNAL_IP:8001"
echo "=================================="
