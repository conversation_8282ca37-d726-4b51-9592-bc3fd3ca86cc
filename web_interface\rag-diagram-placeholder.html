<!DOCTYPE html>
<html>
<head>
    <title>RAG 架構圖佔位符</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .placeholder { 
            width: 1000px; 
            height: 600px; 
            border: 2px dashed #ccc; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            background: #f9f9f9;
            margin: 20px auto;
        }
        .instructions {
            max-width: 800px;
            margin: 0 auto;
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
        }
    </style>
</head>
<body>
    <div class="instructions">
        <h2>📋 如何添加您的 RAG 架構圖</h2>
        <ol>
            <li><strong>保存您的圖片</strong>：將您的 RAG 架構圖保存為 <code>rag-diagram.png</code> 或 <code>rag-diagram.jpg</code></li>
            <li><strong>放置圖片</strong>：將圖片文件放到 <code>web_interface</code> 目錄下</li>
            <li><strong>調整位置</strong>：根據您圖片中各組件的實際位置，調整 HTML 中參數覆蓋層的位置</li>
            <li><strong>測試效果</strong>：刷新頁面查看效果</li>
        </ol>
        
        <h3>🎯 參數位置對應</h3>
        <ul>
            <li><strong>Vector Index top_k</strong>：位於 top: 18%, left: 55%</li>
            <li><strong>BM25 top_k</strong>：位於 top: 35%, left: 55%</li>
            <li><strong>RRF k</strong>：位於 top: 27%, left: 75%</li>
            <li><strong>Reranker top_k</strong>：位於 top: 27%, left: 90%</li>
            <li><strong>LLM 參數</strong>：位於 top: 50%, left: 95%</li>
        </ul>
        
        <p><strong>💡 提示</strong>：您可以在瀏覽器開發者工具中調整這些百分比值，以精確對應您圖片中的位置。</p>
    </div>
    
    <div class="placeholder">
        <div style="text-align: center; color: #666;">
            <h3>請將您的 RAG 架構圖放置在此處</h3>
            <p>文件名：rag-diagram.png 或 rag-diagram.jpg</p>
            <p>建議尺寸：1000x600 像素</p>
        </div>
    </div>
</body>
</html>
