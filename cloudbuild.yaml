# Google Cloud Build 配置文件
steps:
  # 構建 Docker 映像
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-t', 'gcr.io/$PROJECT_ID/rag-api:$COMMIT_SHA',
      '-t', 'gcr.io/$PROJECT_ID/rag-api:latest',
      '.'
    ]

  # 推送映像到 Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/rag-api:$COMMIT_SHA']

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/rag-api:latest']

  # 部署到 Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args: [
      'run', 'deploy', 'rag-api',
      '--image', 'gcr.io/$PROJECT_ID/rag-api:$COMMIT_SHA',
      '--region', 'asia-east1',
      '--platform', 'managed',
      '--allow-unauthenticated',
      '--port', '8001',
      '--memory', '2Gi',
      '--cpu', '2',
      '--max-instances', '10',
      '--set-env-vars', 'DB_HOST=${_DB_HOST},DB_NAME=${_DB_NAME},DB_USER=${_DB_USER},DB_PASSWORD=${_DB_PASSWORD},GEMINI_API_KEY=${_GEMINI_API_KEY},ALLOWED_ORIGINS=${_ALLOWED_ORIGINS}'
    ]

# 替換變數（在 Cloud Build 觸發器中設置）
substitutions:
  _DB_HOST: 'your-db-host'
  _DB_NAME: 'mydb'
  _DB_USER: 'postgres'
  _DB_PASSWORD: 'your-db-password'
  _GEMINI_API_KEY: 'your-gemini-api-key'
  _ALLOWED_ORIGINS: 'https://*.a.run.app'  # 允許所有 Cloud Run 應用訪問
  _ALLOWED_ORIGINS: 'https://*.a.run.app'  # 允許所有 Cloud Run 應用訪問

options:
  logging: CLOUD_LOGGING_ONLY
