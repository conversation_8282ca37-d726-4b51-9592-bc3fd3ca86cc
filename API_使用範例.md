# 🔄 RAG API 雙模式使用範例

## 📋 概述

RAG API 現在支持兩種輸出模式：
1. **完整模式**：RERANK → LLM → API（返回 LLM 生成的回答）
2. **原始模式**：RERANK → API（返回重排序後的原始文檔）

## 🔧 API 端點

### POST `/query`

**請求參數：**
```json
{
    "query_text": "您的查詢文本",
    "include_llm_response": true,  // 是否包含 LLM 處理後的回答
    "raw_documents_only": false,  // 是否只返回原始文檔
    "config_override": {          // 可選：覆蓋配置參數
        "rerank_top_k": 5
    }
}
```

## 📝 使用範例

### 1. 完整模式（默認）- 獲取 LLM 回答

```bash
curl -X POST "http://localhost:8001/query" \
-H "Content-Type: application/json" \
-d '{
    "query_text": "什麼是機器學習？",
    "include_llm_response": true,
    "raw_documents_only": false
}'
```

**響應：**
```json
{
    "response": "機器學習是人工智能的一個分支...",
    "documents": [
        {
            "content": "機器學習是一種數據分析方法...",
            "source": "ml_textbook.pdf",
            "score": 0.95,
            "metadata": {
                "vector_rank": 1,
                "bm25_rank": 2,
                "fused_score": 0.95,
                "rerank_score": 0.98
            }
        }
    ],
    "processing_info": {
        "total_time": 2.5,
        "vector_search_time": 0.3,
        "rerank_time": 0.8,
        "llm_generation_time": 1.2
    }
}
```

### 2. 原始模式 - 只獲取重排序後的文檔

```bash
curl -X POST "http://localhost:8001/query" \
-H "Content-Type: application/json" \
-d '{
    "query_text": "什麼是機器學習？",
    "include_llm_response": false,
    "raw_documents_only": true
}'
```

**響應：**
```json
{
    "response": null,
    "documents": [
        {
            "content": "機器學習是一種數據分析方法，它使計算機能夠在沒有明確編程的情況下學習...",
            "source": "ml_textbook.pdf",
            "score": 0.98,
            "metadata": {
                "vector_rank": 1,
                "bm25_rank": 2,
                "fused_score": 0.95,
                "rerank_score": 0.98,
                "parent_document_id": "doc_123"
            }
        },
        {
            "content": "深度學習是機器學習的一個子集，使用神經網絡...",
            "source": "deep_learning.pdf", 
            "score": 0.92,
            "metadata": {
                "vector_rank": 3,
                "bm25_rank": 1,
                "fused_score": 0.88,
                "rerank_score": 0.92,
                "parent_document_id": "doc_456"
            }
        }
    ],
    "processing_info": {
        "total_time": 1.1,
        "vector_search_time": 0.3,
        "bm25_search_time": 0.2,
        "rrf_fusion_time": 0.1,
        "rerank_time": 0.5,
        "final_results_count": 5
    }
}
```

### 3. 向後兼容模式 - 只獲取 LLM 回答

```bash
curl -X POST "http://localhost:8001/query" \
-H "Content-Type: application/json" \
-d '{
    "query_text": "什麼是機器學習？"
}'
```

**響應：**
```json
{
    "response": "機器學習是人工智能的一個分支，它使計算機能夠從數據中學習並做出預測或決策，而無需明確編程每個步驟...",
    "documents": null,
    "processing_info": null
}
```

## 🎯 使用場景

### 完整模式適用於：
- 需要直接回答的問答系統
- 聊天機器人應用
- 客戶服務自動回覆
- 內容摘要生成

### 原始模式適用於：
- 文檔檢索系統
- 研究資料收集
- 內容推薦引擎
- 自定義後處理流程
- 需要查看原始資料來源的場景

## 🔧 JavaScript 前端範例

```javascript
// 完整模式查詢
async function queryWithLLM(queryText) {
    const response = await fetch('/query', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            query_text: queryText,
            include_llm_response: true,
            raw_documents_only: false
        })
    });
    
    const result = await response.json();
    console.log('LLM 回答:', result.response);
    console.log('相關文檔:', result.documents);
}

// 原始模式查詢
async function queryRawDocuments(queryText) {
    const response = await fetch('/query', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            query_text: queryText,
            include_llm_response: false,
            raw_documents_only: true
        })
    });
    
    const result = await response.json();
    console.log('原始文檔:', result.documents);
    console.log('處理信息:', result.processing_info);
}
```

## 📊 性能對比

| 模式 | 處理時間 | 返回內容 | 適用場景 |
|------|----------|----------|----------|
| 完整模式 | 較長 | LLM回答 + 文檔 | 問答系統 |
| 原始模式 | 較短 | 原始文檔 | 文檔檢索 |
| 兼容模式 | 較長 | 僅LLM回答 | 舊版兼容 |

## 💡 最佳實踐

1. **根據需求選擇模式**：
   - 需要直接答案 → 完整模式
   - 需要原始資料 → 原始模式

2. **性能優化**：
   - 原始模式跳過 LLM 處理，響應更快
   - 可根據用戶需求動態選擇模式

3. **錯誤處理**：
   - 檢查 `response` 和 `documents` 字段是否為 null
   - 利用 `processing_info` 進行性能監控
