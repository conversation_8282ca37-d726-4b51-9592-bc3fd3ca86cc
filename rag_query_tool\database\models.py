"""
models.py

此模組定義了用於 PostgreSQL 資料庫的 SQLAlchemy ORM 模型。
"""

from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID, JSONB, ARRAY
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.sql import func
import uuid

# 導入 pgvector 的向量類型
from pgvector.sqlalchemy import Vector

Base = declarative_base()

class ParentDocument(Base):
    """
    大文檔模型，用於儲存基於標題分割的文本塊。
    """
    __tablename__ = 'parent_documents'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    file_path = Column(Text, nullable=False)
    title = Column(Text, nullable=True)
    page_content = Column(Text, nullable=False)
    document_metadata = Column(JSONB, nullable=True) # 將 metadata 重新命名為 document_metadata
    image_count = Column(Integer, nullable=True)  # 圖片數量
    image_paths = Column(JSONB, nullable=True)    # 圖片路徑列表
    created_at = Column(DateTime(timezone=True), nullable=True)

    # 定義與 ChildDocument 的一對多關係
    children = relationship("ChildDocument", back_populates="parent_doc", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<ParentDocument(id='{self.id}', title='{self.title[:50] if self.title else 'N/A'}...')>"

class ChildDocument(Base):
    """
    小文檔模型，用於儲存逐元素拆分後的文本塊及其向量嵌入。
    """
    __tablename__ = 'child_documents'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    parent_id = Column(UUID(as_uuid=True), ForeignKey('parent_documents.id'), nullable=True)
    content = Column(Text, nullable=False)  # 配合資料庫使用 content 而不是 page_content
    document_metadata = Column(JSONB, nullable=True)
    embedding = Column(Vector(768), nullable=True)
    content_type = Column(Text, nullable=True)  # 新增：內容類型
    image_path = Column(Text, nullable=True)    # 新增：圖片路徑
    image_description = Column(Text, nullable=True)  # 新增：圖片描述
    created_at = Column(DateTime(timezone=True), nullable=True)

    # 定義與 ParentDocument 的多對一關係
    parent_doc = relationship("ParentDocument", back_populates="children")

    # 為了向後兼容，添加 page_content 屬性
    @property
    def page_content(self):
        return self.content

    @page_content.setter
    def page_content(self, value):
        self.content = value

    def __repr__(self):
        return f"<ChildDocument(id='{self.id}', parent_id='{self.parent_id}', content='{self.content[:50] if self.content else ''}...')>"

# 為了方便測試和初始化，可以添加一個函數來創建所有表
def create_all_tables(engine):
    """
    在指定的資料庫引擎上創建所有定義的表。
    """
    Base.metadata.create_all(engine)