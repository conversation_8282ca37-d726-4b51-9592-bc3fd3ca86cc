# 簡化版 Dockerfile - 如果主 Dockerfile 有問題可以使用這個

FROM python:3.11-slim

# 設置工作目錄
WORKDIR /app

# 安裝系統依賴
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# 複製項目文件
COPY pyproject.toml ./
COPY rag_query_tool ./rag_query_tool/
COPY .env* ./

# 安裝 Poetry 並安裝依賴
RUN pip install --no-cache-dir poetry==1.8.3 && \
    poetry config virtualenvs.create false && \
    poetry install --no-dev --no-root

# 暴露端口
EXPOSE 8001

# 健康檢查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# 啟動命令
CMD ["uvicorn", "rag_query_tool.api:app", "--host", "0.0.0.0", "--port", "8001"]
