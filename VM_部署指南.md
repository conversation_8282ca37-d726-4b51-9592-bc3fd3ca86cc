# 🚀 RAG API VM 部署指南

## 📋 概述

本指南說明如何將更新後的 RAG API 部署到 VM，並確保 Cloud Run 的 Web 界面能夠正常連接。

## 🔧 部署步驟

### **步驟 1: 上傳專案到 VM**

```bash
# 將整個專案資料夾上傳到 VM
scp -r /path/to/your/project username@VM_EXTERNAL_IP:/home/<USER>/rag-api
```

### **步驟 2: 在 VM 上部署**

```bash
# SSH 連接到 VM
ssh username@VM_EXTERNAL_IP

# 進入專案目錄
cd /home/<USER>/rag-api

# 執行部署腳本
chmod +x deploy_to_vm.sh
./deploy_to_vm.sh
```

### **步驟 3: 配置防火牆**

```bash
# 執行防火牆配置腳本
chmod +x setup_vm_firewall.sh
./setup_vm_firewall.sh
```

### **步驟 4: 驗證部署**

```bash
# 檢查容器狀態
docker ps | grep rag-api-V1

# 測試健康檢查
curl http://localhost:8001/health

# 查看容器日誌
docker logs rag-api-V1 --tail 20
```

## 🌐 **CORS 配置說明**

### **當前設置**
- **允許來源**: `["*"]` (所有來源)
- **允許方法**: `["GET", "POST", "OPTIONS"]`
- **允許標頭**: `["*"]`

這個設置確保 Cloud Run 的 Web 界面可以正常連接到 VM 上的 API。

## 🔒 **安全考慮**

### **防火牆設置**
- **VM 防火牆**: 開放 8001 端口
- **GCP 防火牆**: 創建 `allow-rag-api-8001` 規則
- **SSH 訪問**: 保留 22 端口

### **建議的安全措施**
1. 定期更新系統和 Docker
2. 監控 API 訪問日誌
3. 考慮添加 API 金鑰驗證
4. 定期備份數據

## 🧪 **測試連接**

### **本地測試**
```bash
# 健康檢查
curl http://VM_EXTERNAL_IP:8001/health

# API 文檔
curl http://VM_EXTERNAL_IP:8001/docs
```

### **Cloud Run 測試**
1. 在 Cloud Run Web 界面中配置 API URL: `http://VM_EXTERNAL_IP:8001`
2. 點擊「測試」按鈕
3. 應該看到「連線成功」

## 🔄 **維護操作**

### **更新 API**
```bash
# 重新部署
./deploy_to_vm.sh
```

### **查看日誌**
```bash
# 實時日誌
docker logs -f rag-api-V1

# 最近日誌
docker logs rag-api-V1 --tail 50
```

### **重啟服務**
```bash
# 重啟容器
docker restart rag-api-V1

# 檢查狀態
docker ps | grep rag-api-V1
```

## 🐛 **故障排除**

### **常見問題**

1. **容器無法啟動**
   ```bash
   docker logs rag-api-V1
   ```

2. **端口無法訪問**
   ```bash
   # 檢查防火牆
   sudo ufw status
   
   # 檢查端口監聽
   ss -tulnp | grep 8001
   ```

3. **CORS 錯誤**
   - 確認 API 的 CORS 設置為 `allow_origins=["*"]`
   - 檢查瀏覽器開發者工具的網路標籤

### **檢查清單**
- [ ] Docker 容器正在運行
- [ ] 端口 8001 正在監聽
- [ ] VM 防火牆開放 8001 端口
- [ ] GCP 防火牆規則已創建
- [ ] 健康檢查返回 200 OK
- [ ] Cloud Run 可以連接到 VM

## 📞 **支持**

如遇問題，請檢查：
1. 容器日誌: `docker logs rag-api-V1`
2. 防火牆狀態: `sudo ufw status`
3. 端口監聽: `ss -tulnp | grep 8001`
4. 網路連通性: `curl http://localhost:8001/health`
