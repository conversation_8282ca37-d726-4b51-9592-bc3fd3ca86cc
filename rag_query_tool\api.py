"""
api.py

此模組定義了 FastAPI 應用程式，用於提供 RAG 查詢的 RESTful API 介面。
"""

from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import os
import tempfile
import uuid
from rag_query_tool.query_tool import get_rag_response, get_rag_response_with_options
from rag_query_tool.config import get_config, update_config, RAGConfig

app = FastAPI(
    title="RAG Query API",
    description="提供基於向量相似度、BM25 和重排序的 RAG 查詢服務。",
    version="1.0.0",
)

# 添加 CORS 中間件 - VM 部署配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # VM 部署允許所有來源
    allow_credentials=False,
    allow_methods=["GET", "POST", "OPTIONS"],
    allow_headers=["*"],
)

class QueryRequest(BaseModel):
    """
    查詢請求模型。
    """
    query_text: str
    config_override: Optional[Dict[str, Any]] = None
    include_llm_response: bool = True  # 是否包含 LLM 處理後的回答
    raw_documents_only: bool = False   # 是否只返回原始文檔（跳過 LLM）

class DocumentInfo(BaseModel):
    """
    文檔信息模型。
    """
    content: str
    source: Optional[str] = None
    score: Optional[float] = None
    metadata: Optional[Dict[str, Any]] = None

class QueryResponse(BaseModel):
    """
    查詢回應模型。
    """
    # LLM 生成的回答（當 include_llm_response=True 時）
    response: Optional[str] = None

    # 重排序後的原始文檔（當 raw_documents_only=True 或 include_llm_response=True 時）
    documents: Optional[List[DocumentInfo]] = None

    # 處理信息
    processing_info: Optional[Dict[str, Any]] = None

class UploadResponse(BaseModel):
    """
    文件上傳回應模型。
    """
    message: str
    file_id: str
    filename: str
    status: str

class FileInfo(BaseModel):
    """
    文件資訊模型。
    """
    id: str
    filename: str
    size: int
    upload_time: str
    status: str

@app.post("/upload", response_model=UploadResponse)
async def upload_file(file: UploadFile = File(...)):
    """
    處理文件上傳請求。

    Args:
        file (UploadFile): 上傳的文件。

    Returns:
        UploadResponse: 包含上傳結果的回應物件。
    """
    # 檢查文件類型
    allowed_extensions = {'.pdf', '.txt', '.docx'}
    file_extension = os.path.splitext(file.filename)[1].lower()

    if file_extension not in allowed_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"不支援的文件格式。支援的格式: {', '.join(allowed_extensions)}"
        )

    try:
        # 生成唯一的文件 ID
        file_id = str(uuid.uuid4())

        # 創建臨時文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        # 這裡應該調用您的文件處理邏輯
        # 由於您的系統可能沒有完整的文件處理流程，我先返回成功狀態
        # 實際使用時需要調用文件讀取、分塊、向量化和存儲到資料庫的流程

        # 清理臨時文件
        os.unlink(temp_file_path)

        return UploadResponse(
            message="文件上傳成功",
            file_id=file_id,
            filename=file.filename,
            status="success"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上傳失敗: {str(e)}")

@app.get("/files", response_model=List[FileInfo])
async def list_files():
    """
    獲取已上傳文件列表。

    Returns:
        List[FileInfo]: 文件資訊列表。
    """
    # 這裡應該從資料庫查詢文件列表
    # 暫時返回空列表
    return []

@app.delete("/files/{file_id}")
async def delete_file(file_id: str):
    """
    刪除指定文件。

    Args:
        file_id (str): 文件 ID。

    Returns:
        dict: 刪除結果。
    """
    # 這裡應該從資料庫刪除文件及相關數據
    # 暫時返回成功狀態
    return {"message": "文件刪除成功", "file_id": file_id}

@app.get("/health")
async def health_check():
    """
    健康檢查端點，用於檢查服務狀態。

    Returns:
        dict: 服務狀態信息
    """
    return {
        "status": "healthy",
        "service": "RAG Query API",
        "timestamp": "2024-01-01T00:00:00Z"
    }

@app.post("/query", response_model=QueryResponse)
async def query_rag(request: QueryRequest):
    """
    處理 RAG 查詢請求，支持兩種模式：
    1. 完整模式：RERANK → LLM → API（返回 LLM 生成的回答）
    2. 原始模式：RERANK → API（返回重排序後的原始文檔）

    Args:
        request (QueryRequest): 包含查詢文本和處理選項的請求物件。

    Returns:
        QueryResponse: 根據請求選項返回不同的內容。
    """
    try:
        # 檢查參數衝突
        if request.raw_documents_only and request.include_llm_response:
            # 如果同時要求原始文檔和 LLM 回答，優先返回原始文檔
            request.include_llm_response = False

        # 調用修改後的查詢函數
        result = await get_rag_response_with_options(
            query_text=request.query_text,
            config_override=request.config_override,
            include_llm_response=request.include_llm_response,
            return_documents=request.raw_documents_only or request.include_llm_response
        )

        # 構建響應
        response = QueryResponse()

        if request.raw_documents_only:
            # 只返回原始文檔
            response.documents = [
                DocumentInfo(
                    content=doc.get('content', ''),
                    source=doc.get('source'),
                    score=doc.get('score'),
                    metadata=doc.get('metadata', {})
                ) for doc in result.get('documents', [])
            ]
            response.processing_info = result.get('processing_info', {})

        elif request.include_llm_response:
            # 返回 LLM 回答和文檔
            response.response = result.get('response', '')
            response.documents = [
                DocumentInfo(
                    content=doc.get('content', ''),
                    source=doc.get('source'),
                    score=doc.get('score'),
                    metadata=doc.get('metadata', {})
                ) for doc in result.get('documents', [])
            ]
            response.processing_info = result.get('processing_info', {})

        else:
            # 只返回 LLM 回答（向後兼容）
            # 如果沒有請求 LLM 回答，需要重新調用
            if not request.include_llm_response:
                result = await get_rag_response_with_options(
                    query_text=request.query_text,
                    config_override=request.config_override,
                    include_llm_response=True,
                    return_documents=False
                )
            response.response = result.get('response', '')

        return response

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查詢處理失敗: {str(e)}")

@app.get("/config", response_model=Dict[str, Any])
async def get_current_config():
    """
    獲取當前 RAG 系統配置。

    Returns:
        Dict[str, Any]: 當前配置參數。
    """
    config = get_config()
    return config.model_dump()

@app.post("/config")
async def update_system_config(config_updates: Dict[str, Any]):
    """
    更新 RAG 系統配置。

    Args:
        config_updates (Dict[str, Any]): 要更新的配置參數。

    Returns:
        Dict[str, str]: 更新結果。
    """
    try:
        success = update_config(**config_updates)
        if success:
            return {"message": "配置更新成功", "status": "success"}
        else:
            raise HTTPException(status_code=400, detail="配置更新失敗")
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"配置更新失敗: {str(e)}")

@app.get("/config/schema")
async def get_config_schema():
    """
    獲取配置參數的結構描述。

    Returns:
        Dict[str, Any]: 配置參數的結構描述，包括類型、範圍、描述等。
    """
    from rag_query_tool.config.rag_config import RAGConfigManager
    config_manager = RAGConfigManager()
    return config_manager.get_parameter_info()

@app.post("/config/reset")
async def reset_config():
    """
    重置配置為默認值。

    Returns:
        Dict[str, str]: 重置結果。
    """
    try:
        from rag_query_tool.config.rag_config import RAGConfigManager
        config_manager = RAGConfigManager()
        success = config_manager.reset_to_default()
        if success:
            return {"message": "配置已重置為默認值", "status": "success"}
        else:
            raise HTTPException(status_code=500, detail="配置重置失敗")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"配置重置失敗: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)  # 改為 8001 端口