# RAG API 環境變數配置範例

# 資料庫配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=your_database_name
DB_USER=postgres
DB_PASSWORD=your_password

# Gemini API 配置
GEMINI_API_KEY=your_gemini_api_key_here

# 模型路徑配置
RERANK_PATH=BAAI/bge-reranker-base
RERANK_ONNX_PATH=rag_query_tool/embeddings/BAAI--bge-reranker-base/onnx_model/model.int8.onnx
LOCAL_EMBEDDING_MODEL_PATH=rag_query_tool/embeddings/bge-base-zh-v1.5

# API 配置
ENVIRONMENT=development
ALLOWED_ORIGINS=*
