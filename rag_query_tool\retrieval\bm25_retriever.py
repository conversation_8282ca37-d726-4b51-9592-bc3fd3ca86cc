"""
bm25_retriever.py

此模組提供基於 BM25 演算法的稀疏檢索功能。
它能夠從文檔集合中，根據查詢文本，返回最相關的文檔。
"""

from typing import List, Dict, Any
from rank_bm25 import BM25Okapi
from rag_query_tool.database.db_manager import DBManager # 修改導入路徑
from rag_query_tool.database.models import ChildDocument # 修改導入路徑
import jieba # 引入 jieba

class BM25Retriever:
    """
    BM25Retriever 類別，用於執行 BM25 稀疏檢索。
    """
    def __init__(self, db_manager: DBManager):
        """
        初始化 BM25Retriever。

        Args:
            db_manager (DBManager): 資料庫管理器實例，用於獲取文檔內容。
        """
        self.db_manager = db_manager
        self.corpus = []
        self.tokenized_corpus = []
        self.bm25 = None
        self._load_corpus()

    def _load_corpus(self):
        """
        從資料庫載入所有 ChildDocument 的內容，並建立 BM25 索引。
        """
        print("載入文檔語料庫以建立 BM25 索引...")
        all_child_documents = self.db_manager.get_all_child_documents()
        # 使用 content 欄位（配合資料庫結構）或 page_content 屬性
        self.corpus = [doc.content for doc in all_child_documents]
        self.document_map = {doc.content: doc for doc in all_child_documents} # 建立內容到文檔的映射
        
        if self.corpus:
            # 使用 jieba 進行分詞
            self.tokenized_corpus = [list(jieba.cut(doc)) for doc in self.corpus]
            self.bm25 = BM25Okapi(self.tokenized_corpus)
            print(f"BM25 索引建立完成，共載入 {len(self.corpus)} 份文檔。")
        else:
            print("資料庫中沒有可用的 ChildDocument，BM25 索引未建立。")

    def retrieve(self, query: str, top_k: int = 3) -> List[ChildDocument]:
        """
        根據查詢文本執行 BM25 檢索。

        Args:
            query (str): 查詢文本。
            top_k (int): 返回最相關文檔的數量。

        Returns:
            List[ChildDocument]: 最相關的 ChildDocument 物件列表。
        """
        if not self.bm25:
            print("BM25 索引未建立，無法執行檢索。")
            return []

        tokenized_query = list(jieba.cut(query)) # 使用 jieba 進行分詞
        doc_scores = self.bm25.get_scores(tokenized_query)

        # 根據分數排序並獲取 top_k 索引
        top_k_indices = doc_scores.argsort()[-top_k:][::-1]

        results = []
        for i in top_k_indices:
            content = self.corpus[i]
            if content in self.document_map:
                results.append(self.document_map[content])
        return results
