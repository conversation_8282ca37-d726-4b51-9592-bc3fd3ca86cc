# --- 階段 1: 構建階段 (安裝依賴) ---
# 使用 Python 3.10 的 slim-buster 版本作为基础镜像，它包含 Debian Buster 操作系统和必要的 Python 环境
FROM python:3.10-slim-buster AS builder

# 设置容器内的工作目录
WORKDIR /app

# 安裝 psycopg2 所需的系統依賴。
# psycopg2 (或其二进制版本 psycopg2-binary) 是 Python 连接 PostgreSQL 的库，
# 通常需要一些 C 编译器和 PostgreSQL 开发库来编译安装。
# build-essential: 编译工具链
# libpq-dev: PostgreSQL 客户端开发库
# gcc: C 编译器
# python3-dev: Python 开发头文件和库
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    build-essential \
    libpq-dev \
    gcc \
    python3-dev \
    cmake \
    pkg-config \
    && rm -rf /var/lib/apt/lists/* # 清理 apt 缓存，减小镜像大小

# 複製 Poetry 项目的配置文件
# pyproject.toml 定义了项目信息和依赖
# poetry.lock 锁定依赖版本
COPY pyproject.toml poetry.lock ./

# === 关键修正：复制你的应用程序源代码 ===
# 这一步将你的 rag_query_tool 文件夹及其内容从宿主机复制到容器的 /app 目录下。
# 确保你的 rag_query_tool 文件夹在 Dockerfile 的同级目录。
COPY rag_query_tool ./rag_query_tool/

# 複製 .env 文件（如果存在）
COPY .env* ./

# 在构建阶段安装 Poetry 本身
# 这是因为在后续的 `poetry install` 命令需要 Poetry 工具来执行
RUN pip install "poetry==1.8.3"

# 配置 Poetry，让它将虚拟环境创建在项目目录（即 /app/.venv）下，而不是全局缓存中
# 这样做有助于在多阶段构建中轻松复制虚拟环境
ENV POETRY_VIRTUALENVS_IN_PROJECT=true
ENV POETRY_VIRTUALENVS_PATH="/app/.venv"

# 先安裝 SentencePiece 以避免依賴問題
RUN pip install --no-cache-dir sentencepiece

# 重新生成鎖定文件以確保相容性，然後安裝依賴
# 先更新鎖定文件，再安裝生產依賴
RUN poetry lock --no-update && \
    poetry install --no-root --only=main

# 清理 Poetry 和 pip 的缓存，进一步减小镜像大小
# 这里清理的是全局缓存，不影响项目内部的虚拟环境
RUN poetry cache clear --all pypi -n && \
    rm -rf /root/.cache/pip

# --- 階段 2: 運行階段 (最終映像) ---
# 从一个干净的 Python 3.10 slim-buster 基础镜像开始，用于最终运行环境
# 这个镜像会更小，因为它不包含构建依赖
FROM python:3.10-slim-buster

# 设置容器内的工作目录
WORKDIR /app

# 安裝運行時所需的系統依賴
# libpq5: PostgreSQL 客户端运行时库
# curl: 用於健康檢查和調試
# ca-certificates: SSL/TLS 證書
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    libpq5 \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* # 清理 apt 缓存

# === 关键步骤：从构建阶段复制所需的文件 ===
# 这一行将 `builder` 阶段的整个 /app 目录（包括 pyproject.toml, poetry.lock,
# 你的应用程序代码 rag_query_tool/ 和 Poetry 创建的 .venv 虚拟环境）
# 复制到最终镜像的 /app 目录。
COPY --from=builder /app /app

# 将 Poetry 创建的虚拟环境的 bin 目录添加到 PATH 环境变量中
# 这样可以直接在最终镜像中运行虚拟环境中的可执行文件（如 uvicorn）
ENV PATH="/app/.venv/bin:${PATH}"

# 创建必要的目录，例如用于存放数据或其他运行时的文件
RUN mkdir -p /app/data

# 设置应用程序监听的端口环境变量
ENV PORT=8001

# 設置 CORS 環境變數（可以在運行時覆蓋）
ENV ALLOWED_ORIGINS="https://*.a.run.app"

# 暴露 FastAPI 应用程序的端口
EXPOSE 8001

# 添加健康檢查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# 定义容器启动时运行的命令
# 直接调用虚拟环境中的 uvicorn 来启动 FastAPI 应用程序
# rag_query_tool.api:app 表示 uvicorn 会在 rag_query_tool 包中寻找 api 模块，并在其中寻找名为 app 的 FastAPI 实例。
CMD ["uvicorn", "rag_query_tool.api:app", "--host", "0.0.0.0", "--port", "8001"]