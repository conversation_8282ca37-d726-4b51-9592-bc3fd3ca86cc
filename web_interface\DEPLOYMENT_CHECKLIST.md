# ✅ RAG Web Interface Cloud Run 部署檢查清單

## 🔧 部署前檢查

### VM API 服務檢查
- [ ] VM 上的 API 服務正常運行 (`sudo supervisorctl status rag-api`)
- [ ] API 健康檢查通過 (`curl http://localhost:8001/health`)
- [ ] 防火牆規則正確配置 (允許來自 Google Cloud 的訪問)
- [ ] Nginx 反向代理正常工作

### 配置文件檢查
- [ ] `deploy.sh` 中的 `PROJECT_ID` 已修改
- [ ] `deploy.sh` 中的 `API_BASE_URL` 已設置為 VM 外部 IP
- [ ] `deploy.sh` 中的 `API_KEY` 已設置
- [ ] `cloudbuild.yaml` 中的替換變數已更新

### Google Cloud 環境檢查
- [ ] 已登入 Google Cloud (`gcloud auth list`)
- [ ] 項目 ID 正確設置 (`gcloud config get-value project`)
- [ ] 必要的 API 已啟用 (Cloud Build, Cloud Run)

## 🚀 部署步驟

### 1. 準備階段
```bash
cd web_interface
```

### 2. 修改配置
編輯 `deploy.sh`:
```bash
PROJECT_ID="your-actual-project-id"
API_BASE_URL="http://YOUR_VM_EXTERNAL_IP"
API_KEY="YOUR_API_KEY_FROM_VM"
```

### 3. 執行部署
```bash
chmod +x deploy.sh
./deploy.sh
```

## 🔍 部署後驗證

### 基本功能檢查
- [ ] Cloud Run 服務部署成功
- [ ] 健康檢查端點正常 (`/health`)
- [ ] 主頁面可以訪問
- [ ] 配置端點返回正確信息 (`/config`)

### 連接測試
- [ ] 前端可以連接到 VM API 服務
- [ ] CORS 設置正確，無跨域錯誤
- [ ] API 金鑰驗證正常工作

### 安全檢查
- [ ] HTTPS 自動重定向正常
- [ ] 安全標頭正確設置
- [ ] 只允許授權的主機訪問

## 🐛 常見問題排除

### 部署失敗
- 檢查 Google Cloud 權限
- 確認項目 ID 正確
- 檢查 Docker 映像構建日誌

### 連接問題
- 確認 VM 外部 IP 可訪問
- 檢查防火牆規則
- 驗證 API 金鑰正確

### CORS 錯誤
- 檢查 VM Nginx CORS 配置
- 確認 Cloud Run 環境變數設置

## 📊 監控設置

### 日誌監控
```bash
# 查看部署日誌
gcloud logging read "resource.type=cloud_run_revision" --limit 20

# 實時監控
gcloud logging tail "resource.type=cloud_run_revision"
```

### 性能監控
- [ ] 設置 Cloud Monitoring 告警
- [ ] 配置錯誤率監控
- [ ] 設置延遲監控

## 🔄 維護操作

### 更新部署
```bash
# 重新部署
gcloud builds submit --config cloudbuild.yaml .
```

### 環境變數更新
```bash
gcloud run services update rag-web-interface \
  --region=asia-east1 \
  --set-env-vars="API_BASE_URL=NEW_VALUE"
```

### 回滾操作
```bash
# 查看版本
gcloud run revisions list --service=rag-web-interface

# 回滾到指定版本
gcloud run services update-traffic rag-web-interface \
  --to-revisions=REVISION_NAME=100
```

## 📞 緊急聯繫

如遇緊急問題：
1. 檢查 Cloud Run 服務狀態
2. 查看最新日誌
3. 確認 VM API 服務狀態
4. 必要時執行回滾操作
