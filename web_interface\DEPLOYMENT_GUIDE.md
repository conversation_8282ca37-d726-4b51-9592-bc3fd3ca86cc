# 🚀 RAG Web Interface Cloud Run 獨立部署指南

## 📋 概述

本指南說明如何將 RAG Web Interface 獨立部署到 Google Cloud Run，與 VM 上的 API 服務配合使用。

## 🏗️ 架構說明

```
┌─────────────────┐    HTTPS    ┌─────────────────┐
│   Cloud Run     │ ──────────▶ │      VM         │
│  (Web 界面)      │             │   (API 服務)     │
│  Port: 8080     │             │   Port: 8001    │
└─────────────────┘             └─────────────────┘
```

## 🔧 部署前準備

### 1. 確保 VM API 服務正常運行
```bash
# 在 VM 上檢查 API 服務狀態
sudo supervisorctl status rag-api
curl http://localhost:8001/health
```

### 2. 獲取 VM 外部 IP
```bash
# 在 GCP Console 或使用 gcloud 命令獲取
gcloud compute instances list
```

### 3. 配置環境變數
編輯以下文件中的配置：

**deploy.sh**:
```bash
API_BASE_URL="http://YOUR_VM_EXTERNAL_IP"
API_KEY="YOUR_API_KEY_FROM_VM"
```

**cloudbuild.yaml**:
```yaml
substitutions:
  _API_BASE_URL: 'http://YOUR_VM_EXTERNAL_IP'
  _API_KEY: 'YOUR_API_KEY_FROM_VM'
```

## 🚀 部署步驟

### 方法 1: 使用部署腳本 (推薦)

```bash
# 1. 進入 web_interface 目錄
cd web_interface

# 2. 修改配置
# 編輯 deploy.sh 中的 PROJECT_ID, API_BASE_URL, API_KEY

# 3. 執行部署
chmod +x deploy.sh
./deploy.sh
```

### 方法 2: 手動部署

```bash
# 1. 設置項目
gcloud config set project YOUR_PROJECT_ID

# 2. 啟用 API
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com

# 3. 構建和部署
gcloud builds submit --config cloudbuild.yaml .
```

## 🔒 安全配置

### 1. CORS 設置
Web 界面已配置為只允許來自指定 VM IP 的 API 請求。

### 2. 安全標頭
自動添加以下安全標頭：
- X-Content-Type-Options: nosniff
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block
- Strict-Transport-Security
- Content-Security-Policy

### 3. API 金鑰驗證
前端會自動在請求中包含 API 金鑰進行驗證。

## 🔧 配置管理

### 環境變數
- `ENVIRONMENT`: production
- `API_BASE_URL`: VM API 服務地址
- `API_KEY`: API 驗證金鑰
- `ALLOWED_HOSTS`: 允許的主機名
- `PORT`: 8080 (Cloud Run 標準)

### 前端配置
Web 界面會自動從 `/config` 端點獲取配置信息。

## 📊 監控和日誌

### 健康檢查
- 端點: `/health`
- 返回服務狀態和版本信息

### 日誌查看
```bash
# 查看 Cloud Run 日誌
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=rag-web-interface" --limit 50

# 實時監控
gcloud logging tail "resource.type=cloud_run_revision AND resource.labels.service_name=rag-web-interface"
```

## 🔄 更新部署

### 更新代碼
```bash
# 重新部署
gcloud builds submit --config cloudbuild.yaml .
```

### 更新環境變數
```bash
gcloud run services update rag-web-interface \
  --region=asia-east1 \
  --set-env-vars="API_BASE_URL=http://NEW_VM_IP,API_KEY=NEW_API_KEY"
```

## 🐛 故障排除

### 常見問題

1. **無法連接到 API 服務**
   - 檢查 VM 防火牆設置
   - 確認 API_BASE_URL 正確
   - 檢查 VM API 服務狀態

2. **CORS 錯誤**
   - 確認 VM Nginx 配置中的 CORS 設置
   - 檢查 Cloud Run 的 ALLOWED_HOSTS 配置

3. **API 金鑰驗證失敗**
   - 確認 API_KEY 環境變數正確設置
   - 檢查 VM 上的 API 金鑰配置

### 檢查清單
- [ ] VM API 服務正常運行
- [ ] VM 外部 IP 可訪問
- [ ] 防火牆規則正確配置
- [ ] 環境變數正確設置
- [ ] Cloud Run 服務部署成功

## 📞 支持

如遇問題，請檢查：
1. Cloud Run 日誌
2. VM API 服務日誌
3. 網路連接狀態
4. 環境變數配置
