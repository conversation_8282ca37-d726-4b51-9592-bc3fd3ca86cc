# Cloud Run Web 連接 VM API 診斷指南

## 🔍 問題診斷步驟

### 1. 檢查 VM 狀態

#### 在 VM 上執行：
```bash
# 檢查 API 服務是否運行
docker ps | grep rag-api

# 檢查端口監聽
sudo ss -tulnp | grep 8001

# 測試本地連接
curl http://localhost:8001/health
```

### 2. 檢查防火牆設置

#### GCP 防火牆規則：
```bash
# 檢查防火牆規則
gcloud compute firewall-rules list --filter="allowed.ports:8001"

# 如果沒有規則，創建一個：
gcloud compute firewall-rules create allow-rag-api \
  --allow tcp:8001 \
  --source-ranges 0.0.0.0/0 \
  --description "Allow RAG API access"
```

#### VM 本地防火牆：
```bash
# 檢查 ufw 狀態
sudo ufw status

# 如果啟用了 ufw，允許端口
sudo ufw allow 8001
```

### 3. 檢查 VM 外部 IP

```bash
# 獲取 VM 外部 IP
curl -s http://checkip.amazonaws.com/

# 或使用 GCP 命令
gcloud compute instances describe YOUR-VM-NAME \
  --zone=YOUR-ZONE \
  --format='get(networkInterfaces[0].accessConfigs[0].natIP)'
```

### 4. 測試外部連接

```bash
# 從外部測試 VM API
curl http://VM-EXTERNAL-IP:8001/health

# 測試 CORS
curl -H "Origin: https://your-cloud-run-app.run.app" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     http://VM-EXTERNAL-IP:8001/query
```

## 🔧 常見問題解決

### 問題 1: 連接被拒絕
**症狀**: `Connection refused` 或 `ERR_CONNECTION_REFUSED`

**解決方案**:
1. 確認 API 服務正在運行
2. 檢查端口是否正確監聽
3. 重啟 API 服務：
   ```bash
   docker restart rag-api-V1
   ```

### 問題 2: 連接超時
**症狀**: `Connection timeout` 或請求一直等待

**解決方案**:
1. 檢查防火牆設置
2. 確認 VM 外部 IP 正確
3. 檢查網路連接

### 問題 3: CORS 錯誤
**症狀**: `CORS policy` 錯誤

**解決方案**:
1. 確認 API 的 CORS 設置：
   ```python
   # 在 api.py 中應該有：
   app.add_middleware(
       CORSMiddleware,
       allow_origins=["*"],
       allow_methods=["GET", "POST", "OPTIONS"],
       allow_headers=["*"],
   )
   ```

### 問題 4: 404 Not Found
**症狀**: API 返回 404 錯誤

**解決方案**:
1. 檢查 API 端點路徑
2. 確認使用正確的端口 (8001)
3. 檢查 API 文檔：`http://VM-IP:8001/docs`

## 🌐 Web 界面配置

### 1. 打開設置面板
- 點擊右下角的 ⚙️ 設置按鈕

### 2. 配置 VM API URL
- **RAG API URL**: `http://YOUR-VM-EXTERNAL-IP:8001`
- **文件 API URL**: `http://YOUR-VM-EXTERNAL-IP:8000` (如果需要)

### 3. 測試連接
- 點擊 "測試" 按鈕
- 查看連接狀態

### 4. 保存設置
- 點擊 "保存設定" 按鈕

## 📋 完整檢查清單

### VM 端檢查：
- [ ] Docker 容器正在運行
- [ ] 端口 8001 正在監聽
- [ ] 本地健康檢查通過
- [ ] GCP 防火牆規則存在
- [ ] VM 防火牆允許端口
- [ ] 外部 IP 可訪問

### Web 界面檢查：
- [ ] VM IP 地址正確
- [ ] 端口號正確 (8001)
- [ ] 使用 http:// 而不是 https://
- [ ] 連接測試通過
- [ ] 設置已保存

### 網路檢查：
- [ ] Cloud Run 可以訪問外部網路
- [ ] VM 在正確的 GCP 項目中
- [ ] 沒有 VPC 網路限制

## 🚨 緊急故障排除

### 如果所有方法都失敗：

1. **重啟 VM**：
   ```bash
   sudo reboot
   ```

2. **重新部署 API**：
   ```bash
   docker stop rag-api-V1
   docker rm rag-api-V1
   docker run -d -p 8001:8001 --name rag-api-V1 rag-api-app
   ```

3. **檢查 VM 日誌**：
   ```bash
   docker logs rag-api-V1 --tail 50
   ```

4. **使用瀏覽器直接測試**：
   - 在瀏覽器中訪問：`http://VM-IP:8001/health`
   - 應該看到 JSON 響應

## 📞 獲取幫助

如果問題仍然存在，請提供以下信息：

1. VM 外部 IP 地址
2. Docker 容器狀態：`docker ps -a`
3. 端口監聽狀態：`sudo ss -tulnp | grep 8001`
4. 防火牆規則：`gcloud compute firewall-rules list`
5. API 日誌：`docker logs rag-api-V1 --tail 20`
6. 瀏覽器控制台錯誤信息

---

*此指南涵蓋了 Cloud Run Web 連接 VM API 的所有常見問題和解決方案。*
