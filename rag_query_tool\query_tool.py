"""
query_tool.py

此模組提供一個簡單的命令行工具，用於執行向量相似度查詢、BM25 稀疏檢索和重排序。
使用者可以輸入查詢文本，程式將返回最相關的 ChildDocument。
"""

import sys
import os
import time # 導入 time 模組
from starlette.concurrency import run_in_threadpool # 導入 run_in_threadpool

# 將專案根目錄添加到 Python 模組搜索路徑
# 假設 query_tool.py 位於 rag/
# 專案根目錄是 rag/ 的父目錄
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, '..'))
sys.path.insert(0, project_root)

from rag_query_tool.database.db_manager import DBManager
from rag_query_tool.embeddings.embedding_generator import EmbeddingGenerator
from rag_query_tool.retrieval.bm25_retriever import BM25Retriever
from rag_query_tool.retrieval.reranker import Reranker
from rag_query_tool.llm.llm_response_generator import LLMResponseGenerator
from rag_query_tool.config import get_config
from dotenv import load_dotenv

# 嘗試載入 .env 檔案，如果不存在則忽略
env_path = os.path.join(os.path.dirname(__file__), '.env')
if os.path.exists(env_path):
    load_dotenv(env_path)
    print(f"已載入環境變數文件: {env_path}")
else:
    print(f"環境變數文件不存在: {env_path}，使用系統環境變數")
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = int(os.getenv("DB_PORT", "5432"))
DB_NAME = os.getenv("DB_NAME", "")
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASSWORD = os.getenv("DB_PASSWORD", "")
RERANK_PATH = os.getenv("RERANK_PATH", "")
RERANK_ONNX_PATH = os.getenv("RERANK_ONNX_PATH", "")
LOCAL_EMBEDDING_MODEL_PATH = os.getenv("LOCAL_EMBEDDING_MODEL_PATH", "")

from typing import List, Dict, Any

# 初始化 RAG 相關組件 (在模組層級初始化，避免每次調用函數都重新初始化)
db_manager = DBManager()
embedding_generator = EmbeddingGenerator()
bm25_retriever = BM25Retriever(db_manager)
reranker = Reranker(
    model_name_or_path=RERANK_PATH,
    onnx_model_path=RERANK_ONNX_PATH
)
llm_generator = LLMResponseGenerator()

def update_models_from_config(config):
    """根據配置更新模型"""
    global reranker, llm_generator

    # 更新 Reranker 模型
    new_onnx_path = config.get_rerank_onnx_path()
    if hasattr(reranker, 'onnx_model_path') and reranker.onnx_model_path != new_onnx_path:
        reranker.reload_model(new_onnx_path)

    # 更新 LLM 模型
    new_llm_model = config.get_llm_model_name()
    if hasattr(llm_generator, 'current_model_name') and llm_generator.current_model_name != new_llm_model:
        llm_generator.switch_model(new_llm_model)

def reciprocal_rank_fusion(results: List[Dict[str, Any]], k=60) -> List[Dict[str, Any]]:
    """
    使用 Reciprocal Rank Fusion (RRF) 演算法融合多個檢索結果。

    Args:
        results (List[Dict[str, Any]]): 包含多個檢索結果的列表，每個結果是一個字典，
                                        其中包含 'doc' (ChildDocument 物件) 和 'rank' (排名)。
        k (int): RRF 演算法中的常數，用於調整排名分數。

    Returns:
        List[Dict[str, Any]]: 融合後的結果列表，按 RRF 分數降序排列。
    """
    fused_scores = {}
    for result in results:
        doc_id = result['doc'].id
        rank = result['rank']
        score = 1.0 / (k + rank)
        fused_scores[doc_id] = fused_scores.get(doc_id, 0.0) + score

    # 根據融合分數排序
    sorted_fused_scores = sorted(fused_scores.items(), key=lambda item: item[1], reverse=True)

    final_results = []
    for doc_id, score in sorted_fused_scores:
        # 找到原始文檔物件並添加融合分數
        for result in results:
            if result['doc'].id == doc_id:
                doc = result['doc']
                # 直接在原對象上添加屬性
                doc.retrieval_method = result['method']  # 保留原始檢索方法
                doc.fused_score = score  # 添加融合分數
                final_results.append(doc)
                break
    return final_results

async def get_rag_response_with_options(
    query_text: str,
    config_override: Dict[str, Any] = None,
    include_llm_response: bool = True,
    return_documents: bool = False
) -> Dict[str, Any]:
    """
    執行 RAG 查詢流程，支持不同的輸出選項。

    Args:
        query_text (str): 用戶的查詢文本。
        config_override (Dict[str, Any], optional): 覆蓋默認配置的參數。
        include_llm_response (bool): 是否包含 LLM 生成的回答。
        return_documents (bool): 是否返回重排序後的文檔。

    Returns:
        Dict[str, Any]: 包含回答和/或文檔的結果字典。
    """
    try:
        # 獲取配置
        config = get_config()
        if config_override:
            # 創建臨時配置副本
            config_dict = config.model_dump()
            config_dict.update(config_override)
            from rag_query_tool.config.rag_config import RAGConfig
            config = RAGConfig(**config_dict)

        # 更新模型
        update_models_from_config(config)

        start_time = time.perf_counter()
        processing_info = {}

        # 1. 執行向量相似度查詢
        stage_start = time.perf_counter()
        query_embedding = await run_in_threadpool(embedding_generator.generate_embedding, query_text)
        vector_raw_results = await run_in_threadpool(db_manager.query_child_documents_by_vector_similarity, query_embedding, top_k=config.vector_top_k)
        vector_results_with_rank = []
        for i, doc in enumerate(vector_raw_results):
            # 為文檔添加排名信息
            doc.vector_rank = i + 1
            # 創建 RRF 需要的格式
            vector_results_with_rank.append({
                'doc': doc,
                'rank': i + 1,
                'method': '向量相似度'
            })

        vector_time = time.perf_counter() - stage_start
        processing_info['vector_search_time'] = vector_time
        processing_info['vector_results_count'] = len(vector_results_with_rank)

        # 2. 執行 BM25 查詢（如果啟用）
        bm25_results_with_rank = []
        if config.enable_bm25:
            stage_start = time.perf_counter()
            bm25_raw_results = await run_in_threadpool(bm25_retriever.retrieve, query_text, top_k=config.bm25_top_k)
            for i, doc in enumerate(bm25_raw_results):
                # 為文檔添加排名信息
                doc.bm25_rank = i + 1
                # 創建 RRF 需要的格式
                bm25_results_with_rank.append({
                    'doc': doc,
                    'rank': i + 1,
                    'method': 'BM25'
                })

            bm25_time = time.perf_counter() - stage_start
            processing_info['bm25_search_time'] = bm25_time
            processing_info['bm25_results_count'] = len(bm25_results_with_rank)

        # 3. RRF 融合
        stage_start = time.perf_counter()
        combined_results = vector_results_with_rank + bm25_results_with_rank
        fused_results = reciprocal_rank_fusion(combined_results, k=config.rrf_k)
        # 限制結果數量
        if len(fused_results) > config.rrf_top_k:
            fused_results = fused_results[:config.rrf_top_k]
        rrf_time = time.perf_counter() - stage_start
        processing_info['rrf_fusion_time'] = rrf_time
        processing_info['rrf_results_count'] = len(fused_results)

        # 4. 重排序（如果啟用）
        final_results = fused_results
        if config.enable_rerank and len(fused_results) > 0:
            stage_start = time.perf_counter()
            # 準備重排序器需要的格式：包含 'doc' 鍵的字典列表
            rerank_input = []
            for doc in fused_results:
                rerank_input.append({
                    'doc': doc,
                    'method': getattr(doc, 'retrieval_method', 'unknown'),
                    'fused_score': getattr(doc, 'fused_score', 0.0)
                })

            # 重排序器不接受 top_k 參數，需要在結果中手動限制
            rerank_results = await run_in_threadpool(
                reranker.rerank,
                query_text,
                rerank_input
            )
            # 限制重排序結果數量
            final_results = rerank_results[:config.rerank_top_k] if len(rerank_results) > config.rerank_top_k else rerank_results
            rerank_time = time.perf_counter() - stage_start
            processing_info['rerank_time'] = rerank_time
            processing_info['final_results_count'] = len(final_results)

        # 準備文檔信息（如果需要返回）
        documents_info = []
        if return_documents:
            for doc in final_results:
                doc_info = {
                    'content': doc.content if hasattr(doc, 'content') else str(doc),
                    'source': getattr(doc, 'source', getattr(doc, 'document_metadata', {}).get('source', None)),
                    'score': getattr(doc, 'fused_score', getattr(doc, 'rerank_score', None)),
                    'metadata': {
                        'vector_rank': getattr(doc, 'vector_rank', None),
                        'bm25_rank': getattr(doc, 'bm25_rank', None),
                        'fused_score': getattr(doc, 'fused_score', None),
                        'rerank_score': getattr(doc, 'rerank_score', None),
                        'parent_document_id': getattr(doc, 'parent_id', None),
                        'document_metadata': getattr(doc, 'document_metadata', {})
                    }
                }
                documents_info.append(doc_info)

        # 5. LLM 生成回答（如果需要）
        llm_response = None
        if include_llm_response and len(final_results) > 0:
            stage_start = time.perf_counter()

            # 準備 LLM 生成器需要的格式：包含 'page_content' 的字典列表
            context_documents = []
            for doc in final_results:
                context_documents.append({
                    'page_content': doc.content
                })

            llm_response = await run_in_threadpool(
                llm_generator.generate_response,
                query_text,
                context_documents,
                temperature=config.llm_temperature,
                max_tokens=config.llm_max_tokens,
                top_p=config.llm_top_p
            )
            llm_time = time.perf_counter() - stage_start
            processing_info['llm_generation_time'] = llm_time

        total_time = time.perf_counter() - start_time
        processing_info['total_time'] = total_time

        # 構建結果
        result = {
            'processing_info': processing_info
        }

        if include_llm_response:
            result['response'] = llm_response

        if return_documents:
            result['documents'] = documents_info

        return result

    except Exception as e:
        print(f"RAG 查詢過程中發生錯誤: {e}")
        raise e

async def get_rag_response(query_text: str, config_override: Dict[str, Any] = None) -> str:
    """
    執行 RAG 查詢流程，並返回 LLM 生成的回覆。
    此函數可供外部模組調用。（向後兼容版本）

    Args:
        query_text (str): 用戶的查詢文本。
        config_override (Dict[str, Any], optional): 覆蓋默認配置的參數。

    Returns:
        str: LLM 生成的回覆文本。
    """
    # 調用新的函數，只返回 LLM 回答
    result = await get_rag_response_with_options(
        query_text=query_text,
        config_override=config_override,
        include_llm_response=True,
        return_documents=False
    )
    return result.get('response', '')

def main():
    """
    主函數，提供互動式查詢、嵌入生成器、BM25 檢索器、重排序器和 LLM 回答生成器，
    並提供互動式查詢介面 (用於命令行測試)。
    """
    # 獲取配置
    config = get_config()

    # 確保環境變數已設定
    if not all([DB_NAME, DB_USER, DB_PASSWORD, DB_HOST, DB_PORT]):
        print("錯誤：請確保所有必要的資料庫環境變數已設定。")
        print("請檢查 .env 文件或系統環境變數。")
        return

    print("歡迎使用混合檢索工具！")
    print("您可以輸入查詢文本，程式將返回最相關的文檔 (向量相似度 + BM25 + 重排序)。")
    print("輸入 'exit' 退出。")

    while True:
        query_text = input("\n請輸入您的查詢文本 (或輸入 'exit' 退出): ")
        if query_text.lower() == 'exit':
            break

        if not query_text.strip():
            print("查詢文本不能為空，請重新輸入。")
            continue
        
        try:
            start_time = time.perf_counter()

            # 1. 執行向量相似度查詢
            stage_start = time.perf_counter()
            query_embedding = embedding_generator.generate_embedding(query_text)
            vector_raw_results = db_manager.query_child_documents_by_vector_similarity(query_embedding, top_k=config.vector_top_k)
            
            vector_results_with_rank = []
            for i, doc in enumerate(vector_raw_results):
                vector_results_with_rank.append({'doc': doc, 'rank': i + 1, 'method': '向量相似度'})
            print(f"時間測量 (main): 向量相似度查詢耗時: {time.perf_counter() - stage_start:.4f} 秒")

            # 2. 執行 BM25 稀疏檢索
            stage_start = time.perf_counter()
            bm25_raw_results = bm25_retriever.retrieve(query_text, top_k=config.bm25_top_k)
            bm25_results_with_rank = []
            for i, doc in enumerate(bm25_raw_results):
                bm25_results_with_rank.append({'doc': doc, 'rank': i + 1, 'method': 'BM25'})
            print(f"時間測量 (main): BM25 稀疏檢索耗時: {time.perf_counter() - stage_start:.4f} 秒")

            # 3. 融合結果 (RRF)
            stage_start = time.perf_counter()
            combined_ranked_results = vector_results_with_rank + bm25_results_with_rank
            rrf_results = reciprocal_rank_fusion(combined_ranked_results, k=config.rrf_k)
            print(f"時間測量 (main): RRF 融合耗時: {time.perf_counter() - stage_start:.4f} 秒")

            if rrf_results:
                print("\n--- RRF 融合結果 (前 6 筆) ---")
                for i, doc in enumerate(rrf_results[:6]):
                    print(f"--- 結果 {i+1} ({getattr(doc, 'retrieval_method', 'N/A')}, RRF Score: {getattr(doc, 'fused_score', 'N/A'):.4f}) ---")
                    print(f"內容: {doc.page_content[:200]}...")
                    print(f"來源: {doc.document_metadata.get('source', 'N/A')}")
                    print(f"頁碼: {doc.document_metadata.get('pages', 'N/A')}")
                    print(f"父文檔 ID: {doc.parent_id}")
                    print("-" * 20)
            else:
                print("RRF 融合未找到相關文檔。")

            # 4. 重排序
            stage_start = time.perf_counter()
            # 限制傳給 Reranker 的文檔數量以提升性能
            rrf_limited_results = rrf_results[:config.rrf_top_k] if len(rrf_results) > config.rrf_top_k else rrf_results
            rerank_input_docs = [{'doc': doc, 'method': doc.retrieval_method, 'fused_score': doc.fused_score} for doc in rrf_limited_results]
            final_reranked_results = reranker.rerank(query_text, rerank_input_docs)
            print(f"時間測量 (main): 重排序耗時: {time.perf_counter() - stage_start:.4f} 秒")

            if final_reranked_results:
                stage_start = time.perf_counter()
                print("\n--- 重排序結果 (前 1 筆) ---")
                for i, doc in enumerate(final_reranked_results[:1]):
                    print(f"--- 結果 {i+1} ({getattr(doc, 'retrieval_method', 'N/A')}, RRF Score: {getattr(doc, 'fused_score', 'N/A'):.4f}, Rerank Score: {getattr(doc, 'rerank_score', 'N/A'):.4f}) ---")
                    print(f"內容: {doc.page_content[:200]}...")
                    print(f"來源: {doc.document_metadata.get('source', 'N/A')}")
                    print(f"頁碼: {doc.document_metadata.get('pages', 'N/A')}")
                    print(f"父文檔 ID: {doc.parent_id}")
                    print("-" * 20)
                
                # 5. 將重排序後的結果的父文檔內容傳給 LLM 處理並回覆
                llm_context_docs = []
                for doc in final_reranked_results[:1]:
                    parent_doc = db_manager.get_parent_document_by_id(doc.parent_id)
                    if parent_doc:
                        llm_context_docs.append({'page_content': parent_doc.page_content})
                print(f"時間測量 (main): 獲取 LLM 上下文耗時: {time.perf_counter() - stage_start:.4f} 秒")

                stage_start = time.perf_counter()
                print("\n--- 傳遞給 LLM 的上下文內容 ---")
                if llm_context_docs:
                    for i, context_doc in enumerate(llm_context_docs):
                        print(f"--- 上下文 {i+1} ---")
                        print(f"{context_doc['page_content'][:500]}...")
                        print("-" * 20)
                else:
                    print("沒有可傳遞給 LLM 的父文檔內容。")

                llm_response = llm_generator.generate_response(
                    query_text,
                    llm_context_docs,
                    config.llm_temperature,
                    config.llm_max_tokens,
                    config.llm_top_p
                )
                print(f"時間測量 (main): LLM 回答生成耗時: {time.perf_counter() - stage_start:.4f} 秒")
                print("\n--- LLM 回答 ---")
                print(llm_response)
                print("-" * 20)
                print(f"時間測量 (main): 總查詢耗時: {time.perf_counter() - start_time:.4f} 秒")

            else:
                print("重排序未找到相關文檔。")
                print(f"時間測量 (main): 總查詢耗時: {time.perf_counter() - start_time:.4f} 秒")

        except Exception as e:
            print(f"查詢過程中發生錯誤: {e}")
            print(f"時間測量 (main): 總查詢耗時: {time.perf_counter() - start_time:.4f} 秒")

    print("查詢工具已退出。")

if __name__ == "__main__":
    main()