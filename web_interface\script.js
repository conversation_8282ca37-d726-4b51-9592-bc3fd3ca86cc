document.addEventListener('DOMContentLoaded', function() {
    // DOM 元素
    const chatInput = document.getElementById('chat-input');
    const sendButton = document.getElementById('send-button');
    const chatMessages = document.getElementById('chat-messages');
    const addSourceBtn = document.getElementById('add-source-btn');
    const uploadSection = document.getElementById('upload-section');
    const uploadZone = document.getElementById('upload-zone');
    const fileInput = document.getElementById('file-input');
    const uploadBtn = document.getElementById('upload-btn');
    const cancelUploadBtn = document.getElementById('cancel-upload-btn');
    const uploadLoading = document.getElementById('upload-loading');
    const uploadResult = document.getElementById('upload-result');
    const fileListContent = document.getElementById('file-list-content');
    const refreshFilesBtn = document.getElementById('refresh-files-btn');
    const collapseParamsBtn = document.getElementById('collapse-params');
    const paramsContent = document.getElementById('params-content');
    
    // API 狀態指示器元素
    const fileApiDot = document.getElementById('file-api-dot');
    const queryApiDot = document.getElementById('query-api-dot');
    const fileApiStatus = document.getElementById('file-api-status');
    const queryApiStatus = document.getElementById('query-api-status');

    // RAG 參數控制元素
    const rerankModelTypeSelect = document.getElementById('rerank-model-type');
    const llmModelTypeSelect = document.getElementById('llm-model-type');
    const vectorTopKSlider = document.getElementById('vector-top-k');
    const bm25TopKSlider = document.getElementById('bm25-top-k');
    const rerankTopKSlider = document.getElementById('rerank-top-k');
    const similarityThresholdSlider = document.getElementById('similarity-threshold');
    const llmTemperatureSlider = document.getElementById('llm-temperature');
    const llmMaxTokensSlider = document.getElementById('llm-max-tokens');
    const llmTopPSlider = document.getElementById('llm-top-p');
    const rrfKSlider = document.getElementById('rrf-k');
    const maxContextLengthSlider = document.getElementById('max-context-length');
    const enableRerankCheckbox = document.getElementById('enable-rerank');
    const enableBm25Checkbox = document.getElementById('enable-bm25');
    const debugModeCheckbox = document.getElementById('debug-mode');

    // 參數操作按鈕
    const applyConfigBtn = document.getElementById('apply-config');
    const resetConfigBtn = document.getElementById('reset-config');
    const loadConfigBtn = document.getElementById('load-config');

    // 說明書相關元素
    const helpGuideBtn = document.getElementById('help-guide-btn');
    const helpGuideModal = document.getElementById('help-guide-modal');
    const closeHelpGuide = document.getElementById('close-help-guide');

    // 連線設置管理
    class ConnectionManager {
        constructor() {
            this.settings = null;
            this.initialized = false;
            this.init();
        }

        async init() {
            this.settings = await this.loadSettings();
            this.initializeEndpoints();
            this.initialized = true;
            console.log('🔗 ConnectionManager 初始化完成:', this.settings);
        }

        async loadSettings() {
            const saved = localStorage.getItem('rag-connection-settings');
            if (saved) {
                return JSON.parse(saved);
            }

            // 嘗試從配置端點獲取設置
            let apiKey = '';
            try {
                const response = await fetch('/config');
                if (response.ok) {
                    const config = await response.json();
                    apiKey = config.apiKey || '';
                }
            } catch (error) {
                console.warn('無法獲取 API 金鑰配置:', error);
            }

            // 預設設置
            const defaultRagUrl = await this.detectDefaultRagUrl();

            // 如果是生產環境且沒有配置 VM IP，顯示設置提示
            if (defaultRagUrl === 'http://YOUR-VM-IP:8001') {
                this.showVMConfigurationGuide();
            }

            return {
                ragApiUrl: defaultRagUrl,
                fileApiUrl: 'http://localhost:8000',
                apiKey: apiKey,
                preset: 'auto'
            };
        }

        showVMConfigurationGuide() {
            // 延遲顯示，確保頁面已載入
            setTimeout(() => {
                const notification = document.createElement('div');
                notification.className = 'vm-config-notification';
                notification.innerHTML = `
                    <div class="notification-content">
                        <h4>🔧 需要配置 VM 連接</h4>
                        <p>請點擊右下角的 <strong>⚙️ 設置</strong> 按鈕，配置您的 VM API 地址</p>
                        <p>格式：<code>http://YOUR-VM-IP:8001</code></p>
                        <button onclick="this.parentElement.parentElement.remove()">知道了</button>
                    </div>
                `;

                // 添加樣式
                notification.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #fff3cd;
                    border: 1px solid #ffeaa7;
                    border-radius: 8px;
                    padding: 16px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    z-index: 1000;
                    max-width: 350px;
                `;

                document.body.appendChild(notification);

                // 10秒後自動消失
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 10000);
            }, 2000);
        }

        async detectDefaultRagUrl() {
            const hostname = window.location.hostname;
            const isProduction = hostname !== 'localhost' && !hostname.startsWith('127.0.0.1');

            if (isProduction) {
                // 生產環境：優先使用代理端點
                console.log('🌐 生產環境檢測到，使用代理端點');
                return `${window.location.origin}/api`;
            } else {
                // 開發環境：使用本地端口
                return 'http://localhost:8001';
            }
        }

        saveSettings() {
            localStorage.setItem('rag-connection-settings', JSON.stringify(this.settings));
            this.initializeEndpoints();
            this.updateStatusIndicators();
        }

        initializeEndpoints() {
            // 更新全局 API 端點
            window.FILE_API_BASE = this.settings.fileApiUrl;
            window.QUERY_API_BASE = this.settings.ragApiUrl;
            window.API_ENDPOINT = `${this.settings.ragApiUrl}/query`;
        }

        async testConnection(url, type) {
            console.log(`🔍 測試連接: ${url} (類型: ${type})`);

            try {
                const testUrl = type === 'rag' ? `${url}/health` : `${url}/health`;
                console.log(`📡 發送請求到: ${testUrl}`);

                const response = await fetch(testUrl, {
                    method: 'GET',
                    mode: 'cors',  // 明確指定 CORS 模式
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    signal: AbortSignal.timeout(15000)  // 增加超時時間到 15 秒
                });

                console.log(`✅ 收到響應: ${response.status} ${response.statusText}`);

                return {
                    success: response.ok,
                    status: response.status,
                    message: response.ok ? '連線成功' : `HTTP ${response.status}: ${response.statusText}`
                };
            } catch (error) {
                console.error('連線測試錯誤:', error);
                console.error('錯誤詳情:', {
                    name: error.name,
                    message: error.message,
                    stack: error.stack
                });

                let errorMessage = '連線失敗';

                if (error.name === 'TypeError') {
                    if (error.message.includes('fetch')) {
                        errorMessage = 'CORS 錯誤或網路不可達';
                        console.error('🔧 診斷建議:');
                        console.error('1. 檢查 VM IP 地址是否正確');
                        console.error('2. 檢查 VM 防火牆是否開放 8001 端口');
                        console.error('3. 檢查 VM 上的 API 服務是否運行');
                        console.error('4. 嘗試在瀏覽器中直接訪問: http://YOUR-VM-IP:8001/health');
                    } else if (error.message.includes('NetworkError')) {
                        errorMessage = '網路錯誤 - VM 可能無法訪問';
                    } else {
                        errorMessage = `網路錯誤: ${error.message}`;
                    }
                } else if (error.name === 'AbortError') {
                    errorMessage = '連線超時 - VM 響應太慢或無法訪問';
                    console.error('🔧 診斷建議: VM 可能負載過高或網路延遲，請稍後重試');
                } else {
                    errorMessage = `${error.name}: ${error.message}`;
                }

                return {
                    success: false,
                    status: 0,
                    message: errorMessage
                };
            }
        }

        updateStatusIndicators() {
            // 更新底部狀態指示器
            const ragStatus = document.querySelector('.status-item[data-api="rag"] .status-dot');
            const fileStatus = document.querySelector('.status-item[data-api="file"] .status-dot');

            if (ragStatus) {
                ragStatus.className = 'status-dot unknown';
                ragStatus.title = `RAG API: ${this.settings.ragApiUrl}`;
            }

            if (fileStatus) {
                fileStatus.className = 'status-dot unknown';
                fileStatus.title = `文件 API: ${this.settings.fileApiUrl}`;
            }
        }

        getPresets() {
            return {
                local: {
                    ragApiUrl: 'http://localhost:8001',
                    fileApiUrl: 'http://localhost:8000',
                    name: '本地開發'
                },
                proxy: {
                    ragApiUrl: `${window.location.origin}/api`,
                    fileApiUrl: `${window.location.origin}/file-api`,
                    name: 'Cloud Run 代理 (推薦)'
                },
                vm: {
                    ragApiUrl: 'https://************',
                    fileApiUrl: 'http://************:8000',
                    name: 'VM HTTPS 直連'
                },
                custom: {
                    ragApiUrl: this.settings.ragApiUrl,
                    fileApiUrl: this.settings.fileApiUrl,
                    name: '自定義'
                }
            };
        }
    }

    // RAG 配置管理
    class RAGConfigManager {
        constructor() {
            this.currentConfig = null;
            this.defaultConfig = null;
            this.isLoading = false;
        }

        async loadConfig() {
            try {
                this.isLoading = true;
                this.showConfigLoading();

                const response = await fetch(`${connectionManager.settings.ragApiUrl}/config`);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                this.currentConfig = await response.json();
                this.updateUI();
                this.hideConfigLoading();
                return this.currentConfig;
            } catch (error) {
                console.error('載入配置失敗:', error);
                this.showConfigError('載入配置失敗: ' + error.message);
                this.hideConfigLoading();
                throw error;
            } finally {
                this.isLoading = false;
            }
        }

        async updateConfig(configUpdates) {
            try {
                this.showConfigLoading();

                const response = await fetch(`${connectionManager.settings.ragApiUrl}/config`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(configUpdates)
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }

                const result = await response.json();
                await this.loadConfig(); // 重新載入配置
                this.showConfigSuccess('配置更新成功');
                return result;
            } catch (error) {
                console.error('更新配置失敗:', error);
                this.showConfigError('更新配置失敗: ' + error.message);
                throw error;
            } finally {
                this.hideConfigLoading();
            }
        }

        async resetConfig() {
            try {
                this.showConfigLoading();

                const response = await fetch(`${connectionManager.settings.ragApiUrl}/config/reset`, {
                    method: 'POST'
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }

                await this.loadConfig(); // 重新載入配置
                this.showConfigSuccess('配置已重置為默認值');
            } catch (error) {
                console.error('重置配置失敗:', error);
                this.showConfigError('重置配置失敗: ' + error.message);
                throw error;
            } finally {
                this.hideConfigLoading();
            }
        }

        getCurrentConfig() {
            if (!this.currentConfig) {
                return {};
            }

            return {
                rerank_model_type: rerankModelTypeSelect?.value || 'onnx_int8',
                llm_model_type: llmModelTypeSelect?.value || 'gemini-2.0-flash',
                vector_top_k: parseInt(vectorTopKSlider?.value || 10),
                bm25_top_k: parseInt(bm25TopKSlider?.value || 10),
                rerank_top_k: parseInt(rerankTopKSlider?.value || 5),
                similarity_threshold: parseFloat(similarityThresholdSlider?.value || 0.0),
                llm_temperature: parseFloat(llmTemperatureSlider?.value || 0.7),
                llm_max_tokens: parseInt(llmMaxTokensSlider?.value || 1000),
                llm_top_p: parseFloat(llmTopPSlider?.value || 0.9),
                rrf_k: parseInt(rrfKSlider?.value || 60),
                max_context_length: parseInt(maxContextLengthSlider?.value || 3000),
                enable_rerank: enableRerankCheckbox?.checked || true,
                enable_bm25: enableBm25Checkbox?.checked || true,
                debug_mode: debugModeCheckbox?.checked || false
            };
        }

        updateUI() {
            if (!this.currentConfig) return;

            // 更新模型選擇器
            if (rerankModelTypeSelect) rerankModelTypeSelect.value = this.currentConfig.rerank_model_type || 'onnx_int8';
            if (llmModelTypeSelect) llmModelTypeSelect.value = this.currentConfig.llm_model_type || 'gemini-2.0-flash';

            // 更新滑桿值
            this.updateSlider(vectorTopKSlider, this.currentConfig.vector_top_k);
            this.updateSlider(bm25TopKSlider, this.currentConfig.bm25_top_k);
            this.updateSlider(rerankTopKSlider, this.currentConfig.rerank_top_k);
            this.updateSlider(similarityThresholdSlider, this.currentConfig.similarity_threshold);
            this.updateSlider(llmTemperatureSlider, this.currentConfig.llm_temperature);
            this.updateSlider(llmMaxTokensSlider, this.currentConfig.llm_max_tokens);
            this.updateSlider(llmTopPSlider, this.currentConfig.llm_top_p);
            this.updateSlider(rrfKSlider, this.currentConfig.rrf_k);
            this.updateSlider(maxContextLengthSlider, this.currentConfig.max_context_length);

            // 更新複選框
            if (enableRerankCheckbox) enableRerankCheckbox.checked = this.currentConfig.enable_rerank;
            if (enableBm25Checkbox) enableBm25Checkbox.checked = this.currentConfig.enable_bm25;
            if (debugModeCheckbox) debugModeCheckbox.checked = this.currentConfig.debug_mode;
        }

        updateSlider(slider, value) {
            if (slider && value !== undefined) {
                slider.value = value;
                const valueSpan = slider.parentElement.querySelector('.param-value');
                if (valueSpan) {
                    valueSpan.textContent = value;
                }
            }
        }

        showConfigLoading() {
            const paramsContent = document.getElementById('params-content');
            if (paramsContent) {
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'param-loading';
                loadingDiv.id = 'config-loading';
                loadingDiv.innerHTML = '<div class="spinner"></div>載入配置中...';
                paramsContent.insertBefore(loadingDiv, paramsContent.firstChild);
            }
        }

        hideConfigLoading() {
            const loadingDiv = document.getElementById('config-loading');
            if (loadingDiv) {
                loadingDiv.remove();
            }
        }

        showConfigSuccess(message) {
            showNotification(message, 'success');
        }

        showConfigError(message) {
            showNotification(message, 'error');
        }
    }

    // 初始化管理器
    const connectionManager = new ConnectionManager();
    const ragConfigManager = new RAGConfigManager();

    // 等待連線管理器初始化完成
    async function waitForConnectionManager() {
        while (!connectionManager.initialized) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        return connectionManager;
    }

    // API 端點配置（向後兼容）
    let FILE_API_BASE = window.FILE_API_BASE;
    let QUERY_API_BASE = window.QUERY_API_BASE;
    let API_ENDPOINT = window.API_ENDPOINT;

    // 應用狀態
    let uploadedSources = [];
    let conversationHistory = [];
    let isParamsCollapsed = false;
    let apiStatusCheckInterval = null;
    let isUploading = false;

    // 初始化
    init();

    async function init() {
        // 等待連線管理器初始化完成
        await waitForConnectionManager();

        setupEventListeners();
        setupParameterControls();
        updateSendButtonState();

        // 初始化 API 狀態指示器
        initApiStatusIndicator();

        // 載入文件列表
        loadFileList();

        // 自動聚焦到輸入框
        chatInput.focus();

        console.log('✅ 應用初始化完成');
    }

    function setupEventListeners() {
        // 聊天相關
        sendButton.addEventListener('click', sendMessage);
        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        chatInput.addEventListener('input', updateSendButtonState);

        // 文件上傳相關
        addSourceBtn.addEventListener('click', () => {
            uploadSection.style.display = uploadSection.style.display === 'none' ? 'block' : 'none';
        });

        uploadZone.addEventListener('click', () => fileInput.click());
        uploadZone.addEventListener('dragover', handleDragOver);
        uploadZone.addEventListener('drop', handleDrop);
        fileInput.addEventListener('change', handleFileSelect);
        uploadBtn.addEventListener('click', uploadFile);
        cancelUploadBtn.addEventListener('click', () => {
            uploadSection.style.display = 'none';
        });

        // 文件列表相關
        refreshFilesBtn.addEventListener('click', loadFileList);

        // 參數控制相關
        if (collapseParamsBtn) {
            collapseParamsBtn.addEventListener('click', toggleParams);
        }

        // 說明書相關
        if (helpGuideBtn) {
            helpGuideBtn.addEventListener('click', showHelpGuide);
        }
        if (closeHelpGuide) {
            closeHelpGuide.addEventListener('click', hideHelpGuide);
        }
        if (helpGuideModal) {
            helpGuideModal.addEventListener('click', function(e) {
                if (e.target === helpGuideModal) {
                    hideHelpGuide();
                }
            });
        }
    }

    function setupParameterControls() {
        // 設置所有滑桿的事件監聽器
        const sliders = [
            { slider: vectorTopKSlider, name: 'vector-top-k' },
            { slider: bm25TopKSlider, name: 'bm25-top-k' },
            { slider: rerankTopKSlider, name: 'rerank-top-k' },
            { slider: similarityThresholdSlider, name: 'similarity-threshold' },
            { slider: llmTemperatureSlider, name: 'llm-temperature' },
            { slider: llmMaxTokensSlider, name: 'llm-max-tokens' },
            { slider: llmTopPSlider, name: 'llm-top-p' },
            { slider: rrfKSlider, name: 'rrf-k' },
            { slider: maxContextLengthSlider, name: 'max-context-length' }
        ];

        sliders.forEach(({ slider, name }) => {
            if (slider) {
                slider.addEventListener('input', function() {
                    const valueSpan = this.parentElement.querySelector('.param-value');
                    if (valueSpan) {
                        valueSpan.textContent = this.value;
                    }
                });
            }
        });

        // 參數操作按鈕事件
        if (applyConfigBtn) {
            applyConfigBtn.addEventListener('click', async () => {
                try {
                    const config = ragConfigManager.getCurrentConfig();
                    await ragConfigManager.updateConfig(config);
                } catch (error) {
                    console.error('應用配置失敗:', error);
                }
            });
        }

        if (resetConfigBtn) {
            resetConfigBtn.addEventListener('click', async () => {
                if (confirm('確定要重置所有參數為默認值嗎？')) {
                    try {
                        await ragConfigManager.resetConfig();
                    } catch (error) {
                        console.error('重置配置失敗:', error);
                    }
                }
            });
        }

        if (loadConfigBtn) {
            loadConfigBtn.addEventListener('click', async () => {
                try {
                    await ragConfigManager.loadConfig();
                } catch (error) {
                    console.error('載入配置失敗:', error);
                }
            });
        }

        // 初始載入配置
        ragConfigManager.loadConfig().catch(error => {
            console.error('初始載入配置失敗:', error);
        });
    }

    // 基本功能實現
    async function sendMessage() {
        const message = chatInput.value.trim();
        if (!message) return;

        // 添加用戶消息
        addMessage(message, 'user');
        chatInput.value = '';
        updateSendButtonState();

        // 顯示 AI 正在思考的動畫
        const thinkingMessage = addThinkingMessage();

        try {
            const response = await fetch(API_ENDPOINT, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    query_text: message,
                    config_override: ragConfigManager.getCurrentConfig()
                })
            });

            // 移除思考動畫
            removeThinkingMessage(thinkingMessage);

            if (response.ok) {
                const result = await response.json();
                addMessage(result.response, 'assistant');
            } else {
                addMessage('抱歉，發生了錯誤。請稍後再試。', 'assistant');
            }
        } catch (error) {
            console.error('發送消息錯誤:', error);

            // 移除思考動畫
            removeThinkingMessage(thinkingMessage);

            addMessage('網路連接錯誤，請檢查您的連接。', 'assistant');
        }
    }

    function addMessage(content, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';

        // 處理文字格式，保留換行和基本格式
        if (sender === 'assistant') {
            // 將換行轉換為 <br> 標籤，並處理段落
            const formattedContent = content
                .replace(/\n\n/g, '</p><p>')  // 雙換行轉為段落
                .replace(/\n/g, '<br>')       // 單換行轉為 <br>
                .replace(/^\s*/, '<p>')       // 開頭加 <p>
                .replace(/\s*$/, '</p>');     // 結尾加 </p>
            contentDiv.innerHTML = formattedContent;
        } else {
            contentDiv.textContent = content;
        }

        messageDiv.appendChild(contentDiv);
        chatMessages.appendChild(messageDiv);

        // 滾動到底部
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // 更新對話歷史
        conversationHistory.push({ sender, content });
    }

    // 添加思考動畫
    function addThinkingMessage() {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message assistant thinking';

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';

        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'thinking-animation';
        loadingDiv.innerHTML = `
            <div class="loading-dot"></div>
            <div class="loading-dot"></div>
            <div class="loading-dot"></div>
        `;

        contentDiv.appendChild(loadingDiv);
        messageDiv.appendChild(contentDiv);
        chatMessages.appendChild(messageDiv);

        // 滾動到底部
        chatMessages.scrollTop = chatMessages.scrollHeight;

        return messageDiv;
    }

    // 移除思考動畫
    function removeThinkingMessage(thinkingMessage) {
        if (thinkingMessage && thinkingMessage.parentNode) {
            thinkingMessage.remove();
        }
    }

    function updateSendButtonState() {
        const hasText = chatInput.value.trim().length > 0;
        sendButton.disabled = !hasText;
    }

    // 文件上傳功能
    function handleDragOver(e) {
        e.preventDefault();
        uploadZone.classList.add('drag-over');
    }

    function handleDrop(e) {
        e.preventDefault();
        uploadZone.classList.remove('drag-over');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            updateUploadButtonState();
        }
    }

    function handleFileSelect(e) {
        updateUploadButtonState();
    }

    function updateUploadButtonState() {
        const hasFile = fileInput.files && fileInput.files.length > 0;
        uploadBtn.disabled = !hasFile;
    }

    async function uploadFile() {
        const file = fileInput.files[0];
        if (!file) return;

        // 檢查文件類型
        const allowedTypes = ['.pdf', '.txt', '.doc', '.docx'];
        const fileExt = '.' + file.name.split('.').pop().toLowerCase();
        if (!allowedTypes.includes(fileExt)) {
            showUploadResult(`不支援的文件類型: ${fileExt}。支援的類型: ${allowedTypes.join(', ')}`, 'error');
            return;
        }

        hideUploadResult();
        
        // 設置上傳狀態
        isUploading = true;
        
        // 設置文件 API 為處理中狀態
        setApiStatus('file', 'checking');
        
        // 禁用上傳按鈕防止重複提交
        uploadBtn.disabled = true;
        uploadBtn.textContent = '處理中...';
        
        // 添加上傳中的文件項目
        addUploadingFileItem(file);

        const formData = new FormData();
        formData.append('file', file);

        try {
            const response = await fetch(`${FILE_API_BASE}/upload`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (response.ok) {
                if (result.success) {
                    // 更新上傳項目為成功狀態
                    updateUploadingItemToSuccess();
                    
                    showUploadResult('✅ 文件處理成功！', 'success');
                    
                    // 顯示處理統計
                    if (result.processing_stats) {
                        showProcessingStats(result.processing_stats);
                    }
                    
                    // 顯示成功通知
                    showNotification('文件上傳並處理成功！', 'success');
                    
                    // 重置上傳表單
                    fileInput.value = '';
                    updateUploadButtonState();
                    
                    // 更新發送按鈕狀態
                    updateSendButtonState();
                } else {
                    // 移除上傳項目
                    removeUploadingItem();
                    
                    // 處理重複文件等情況
                    showUploadResult(`ℹ️ ${result.message}`, 'info');
                    if (result.file_hash) {
                        const infoHtml = `<div style="margin-top: 8px; font-size: 12px; color: #5f6368;">文件雜湊值: ${result.file_hash}</div>`;
                        uploadResult.innerHTML += infoHtml;
                    }
                    
                    // 重新載入文件列表
                    loadFileList();
                }
            } else {
                // 移除上傳項目
                removeUploadingItem();
                
                // 處理 HTTP 錯誤
                const errorMsg = result.detail || result.message || '未知錯誤';
                showUploadResult(`❌ 處理失敗: ${errorMsg}`, 'error');
            }

        } catch (error) {
            console.error('上傳錯誤:', error);
            
            // 移除上傳項目
            removeUploadingItem();
            
            showUploadResult(`網路錯誤: ${error.message}`, 'error');
        } finally {
            // 重置上傳狀態
            isUploading = false;
            
            // 確保動畫停止
            showUploadLoading(false);
            uploadBtn.disabled = false;
            uploadBtn.textContent = '上傳並處理';
            
            // 立即檢查一次 API 狀態
            setTimeout(() => {
                checkApiStatus();
            }, 1000);
        }
    }

    function showUploadLoading(show) {
        uploadLoading.style.display = show ? 'block' : 'none';
    }
    
    function addUploadingFileItem(file) {
        const fileExt = file.name.split('.').pop().toLowerCase();
        const fileIcon = getFileTypeIcon(fileExt);
        const fileSize = (file.size / 1024).toFixed(1);
        
        const uploadingItem = document.createElement('div');
        uploadingItem.className = 'file-item-compact uploading';
        uploadingItem.id = 'uploading-item';
        uploadingItem.innerHTML = `
            <div class="file-icon">${fileIcon}</div>
            <div class="file-info">
                <div class="file-name">${file.name}</div>
                <div class="file-meta">${fileSize} KB</div>
            </div>
            <div class="file-status uploading">
                <div class="spinner"></div>
            </div>
        `;
        
        // 插入到文件列表的開頭
        if (fileListContent.querySelector('.empty-state')) {
            fileListContent.innerHTML = '';
        }
        fileListContent.insertBefore(uploadingItem, fileListContent.firstChild);
    }
    
    function updateUploadingItemToSuccess() {
        const uploadingItem = document.getElementById('uploading-item');
        if (uploadingItem) {
            const statusElement = uploadingItem.querySelector('.file-status');
            statusElement.className = 'file-status success-animation';
            statusElement.innerHTML = '✅';
            
            // 2秒後移除，讓正常的文件列表顯示
            setTimeout(() => {
                if (uploadingItem && uploadingItem.parentNode) {
                    uploadingItem.remove();
                }
                loadFileList(); // 重新載入完整列表
            }, 2000);
        }
    }
    
    function removeUploadingItem() {
        const uploadingItem = document.getElementById('uploading-item');
        if (uploadingItem && uploadingItem.parentNode) {
            uploadingItem.remove();
        }
    }

    function showUploadResult(message, type) {
        uploadResult.className = `upload-result ${type}`;
        uploadResult.innerHTML = message;
        uploadResult.style.display = 'block';
    }

    function hideUploadResult() {
        uploadResult.style.display = 'none';
    }

    function showProcessingStats(stats) {
        if (!stats) return;
        
        let statsHtml = '<div class="processing-stats"><h4>📊 處理統計</h4><ul>';
        
        // 根據您的 API 返回的統計格式進行顯示
        const displayNames = {
            'file_type': '📄 文件類型',
            'parent_documents': '📑 父文檔數量',
            'child_documents': '📝 子文檔數量',
            'images_processed': '🖼️ 處理圖片數',
            'vector_dimension': '🔢 向量維度',
            'split_method': '✂️ 分割方式',
            'titles_detected': '📋 檢測標題數'
        };
        
        for (const [key, value] of Object.entries(stats)) {
            const displayKey = displayNames[key] || key;
            statsHtml += `<li><strong>${displayKey}:</strong> ${value}</li>`;
        }
        
        statsHtml += '</ul></div>';
        uploadResult.innerHTML += statsHtml;
    }

    // 文件列表管理
    async function loadFileList() {
        try {
            const response = await fetch(`${FILE_API_BASE}/files`);
            const result = await response.json();

            console.log('API 返回的文件列表數據:', result);

            if (result.files && result.files.length > 0) {
                console.log('第一個文件的數據結構:', result.files[0]);
                displayFileList(result.files, result.total);
            } else {
                showEmptyFileList();
            }
        } catch (error) {
            console.error('載入文件列表失敗:', error);
            showEmptyFileList();
        }
    }

    function displayFileList(files, total) {
        if (files.length === 0) {
            fileListContent.innerHTML = `
                <div class="empty-state">
                    <p>尚未上傳任何資料</p>
                    <p class="hint">點擊上方按鈕開始上傳文件</p>
                </div>
            `;
            return;
        }

        let html = '';
        files.forEach(file => {
            const processedDate = new Date(file.processed_at).toLocaleString('zh-TW');
            const fileSize = (file.size / 1024).toFixed(1);
            const dbStats = file.database_stats;

            // 文件類型圖標
            const fileExt = file.filename.split('.').pop().toLowerCase();
            const fileIcon = getFileTypeIcon(fileExt);
            
            // 處理狀態
            const isProcessed = dbStats && dbStats.exists;
            const statusIcon = isProcessed ? '✅' : '⚪';
            const statusClass = isProcessed ? 'processed' : 'processing';

            // 將文件數據存儲到全局變量中
            window.fileData = window.fileData || {};
            const fileId = `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            window.fileData[fileId] = file;

            html += `
                <div class="file-item-compact" onclick="showFileInfoInChat('${fileId}')">
                    <div class="file-icon">${fileIcon}</div>
                    <div class="file-info">
                        <div class="file-name">${file.original_name}</div>
                        <div class="file-meta">${fileSize} KB</div>
                    </div>
                    <div class="file-status ${statusClass}" title="${isProcessed ? '處理完成' : '處理中'}">
                        ${statusIcon}
                    </div>
                    <button class="delete-btn-compact" onclick="event.stopPropagation(); deleteFile('${file.filename}', '${file.original_name}')" title="刪除文件">
                        <span class="material-icons">close</span>
                    </button>
                </div>
            `;
        });

        fileListContent.innerHTML = html;
    }

    function getFileTypeIcon(extension) {
        const icons = {
            'pdf': '📕',
            'txt': '📄',
            'doc': '📘',
            'docx': '📘'
        };
        return icons[extension] || '📄';
    }

    function showEmptyFileList() {
        fileListContent.innerHTML = `
            <div class="empty-state">
                <p>尚未上傳任何資料</p>
                <p class="hint">點擊上方按鈕開始上傳文件</p>
            </div>
        `;
    }

    // API 狀態指示器管理
    function initApiStatusIndicator() {
        // 設置初始狀態
        setApiStatus('file', 'checking');
        setApiStatus('query', 'checking');
        
        // 立即檢查一次
        checkApiStatus();
        
        // 設置定期檢查（每30秒）
        apiStatusCheckInterval = setInterval(checkApiStatus, 30000);
        
        // 添加點擊事件
        fileApiStatus.addEventListener('click', () => checkSingleApiStatus('file'));
        queryApiStatus.addEventListener('click', () => checkSingleApiStatus('query'));
    }
    
    function setApiStatus(apiType, status) {
        const dot = apiType === 'file' ? fileApiDot : queryApiDot;
        const statusItem = apiType === 'file' ? fileApiStatus : queryApiStatus;
        
        // 移除所有狀態類
        dot.classList.remove('online', 'offline', 'checking');
        
        // 添加新狀態
        dot.classList.add(status);
        
        // 更新 tooltip
        const apiName = apiType === 'file' ? '文件處理 API (8000)' : 'RAG 查詢 API (8001)';
        let statusText = {
            'online': '在線',
            'offline': '離線',
            'checking': '檢查中'
        }[status];
        
        // 如果是文件 API 且正在上傳，顯示特殊狀態
        if (apiType === 'file' && isUploading && status === 'checking') {
            statusText = '處理文件中';
        }
        
        statusItem.title = `${apiName} - ${statusText}`;
    }

    async function checkApiStatus() {
        // 如果正在上傳，跳過文件 API 檢查
        if (!isUploading) {
            checkSingleApiStatus('file');
        }
        
        // 查詢 API 不受上傳影響
        checkSingleApiStatus('query');
    }

    async function checkSingleApiStatus(apiType) {
        setApiStatus(apiType, 'checking');
        
        try {
            let response;
            
            if (apiType === 'file') {
                // 檢查文件處理 API - 使用 health 端點
                response = await fetch(`${FILE_API_BASE}/health`, {
                    method: 'GET',
                    signal: AbortSignal.timeout(5000) // 5秒超時
                });
            } else {
                // 檢查查詢 API - 使用專門的健康檢查端點
                // 避免觸發實際的 RAG 查詢處理
                response = await fetch(`${QUERY_API_BASE}/health`, {
                    method: 'GET',
                    signal: AbortSignal.timeout(5000) // 5秒超時
                });
            }
            
            if (response.ok) {
                setApiStatus(apiType, 'online');
            } else {
                setApiStatus(apiType, 'offline');
            }
            
        } catch (error) {
            console.log(`${apiType} API 檢查失敗:`, error.message);
            setApiStatus(apiType, 'offline');
        }
    }

    // 在聊天框中顯示文件詳細信息
    window.showFileInfoInChat = function(fileId) {
        try {
            console.log('點擊文件 ID:', fileId);

            const file = window.fileData[fileId];
            if (!file) {
                throw new Error('找不到文件數據');
            }

            console.log('文件數據:', file);

            const processedDate = new Date(file.processed_at).toLocaleString('zh-TW');
            const fileSize = (file.size / 1024).toFixed(1);
            const dbStats = file.database_stats;

            const fileExt = file.filename.split('.').pop().toLowerCase();
            const fileIcon = getFileTypeIcon(fileExt);

            // 構建詳細信息消息
            let infoMessage = `📋 **文件詳細信息**\n\n`;
            infoMessage += `${fileIcon} **文件名稱**: ${file.original_name}\n`;
            infoMessage += `📄 **文件類型**: ${fileExt.toUpperCase()}\n`;
            infoMessage += `💾 **文件大小**: ${fileSize} KB\n`;
            infoMessage += `⏰ **處理時間**: ${processedDate}\n\n`;

            // 資料庫統計
            if (dbStats && dbStats.exists) {
                infoMessage += `🗄️ **處理統計**:\n`;
                infoMessage += `• 📑 父文檔數量: ${dbStats.parent_documents || 0}\n`;
                infoMessage += `• 📝 子文檔數量: ${dbStats.child_documents || 0}\n`;
                infoMessage += `• 🖼️ 圖片數量: ${dbStats.images || 0}\n`;
            } else {
                infoMessage += `⚠️ **狀態**: 資料庫中無相關記錄\n`;
            }

            console.log('準備顯示信息:', infoMessage);

            // 添加到聊天記錄
            addFileInfoMessage(infoMessage);

        } catch (error) {
            console.error('顯示文件詳細信息時出錯:', error);

            // 顯示更詳細的錯誤信息
            addFileInfoMessage(`❌ **無法顯示文件詳細信息**\n\n錯誤: ${error.message}`);
        }
    };

    // 添加文件信息消息到聊天框
    function addFileInfoMessage(content) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message system file-info';

        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';

        // 將換行符轉換為 HTML
        const htmlContent = content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\n/g, '<br>');

        contentDiv.innerHTML = htmlContent;

        messageDiv.appendChild(contentDiv);
        chatMessages.appendChild(messageDiv);

        // 滾動到底部
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // 全域函數，供 HTML onclick 使用
    window.deleteFile = async function(filename, originalName) {
        if (!confirm(`確定要刪除文件 "${originalName}" 嗎？\n\n這將會刪除：\n- OUTPUT 目錄中的文件\n- 資料庫中的所有相關記錄\n- 相關的圖片文件\n\n此操作無法復原！`)) {
            return;
        }

        try {
            showUploadResult('正在刪除文件...', 'info');

            const response = await fetch(`${FILE_API_BASE}/files/${encodeURIComponent(filename)}`, {
                method: 'DELETE'
            });

            const result = await response.json();

            if (response.ok && result.success) {
                showUploadResult(`✅ 已刪除 "${originalName}"`, 'success');

                // 重新載入文件列表
                loadFileList();
            } else {
                showUploadResult(`❌ 刪除失敗: ${result.detail || result.message}`, 'error');
            }

        } catch (error) {
            showUploadResult(`刪除失敗: ${error.message}`, 'error');
        }
    };

    // 參數控制
    function toggleParams() {
        isParamsCollapsed = !isParamsCollapsed;
        paramsContent.style.display = isParamsCollapsed ? 'none' : 'block';
        collapseParamsBtn.querySelector('.material-icons').textContent =
            isParamsCollapsed ? 'expand_more' : 'expand_less';
    }

    // 說明書功能
    function showHelpGuide() {
        if (helpGuideModal) {
            helpGuideModal.classList.add('show');
            document.body.style.overflow = 'hidden';
        }
    }

    function hideHelpGuide() {
        if (helpGuideModal) {
            helpGuideModal.classList.remove('show');
            document.body.style.overflow = '';
        }
    }

    // 通知系統
    function showNotification(message, type = 'info') {
        // 創建通知元素
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <span class="notification-message">${message}</span>
            <button class="notification-close" onclick="this.parentElement.remove()">
                <span class="material-icons">close</span>
            </button>
        `;
        
        // 添加到頁面
        document.body.appendChild(notification);
        
        // 3秒後自動移除
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 3000);
    }

    // 測試 API 連接（現在由狀態指示器處理，保留用於手動測試）
    function testApiConnection() {
        console.log('手動測試 API 連接...');
        checkApiStatus();
    }

    // 響應式設計支持
    function handleResize() {
        if (window.innerWidth <= 768) {
            // 移動端處理
            document.body.classList.add('mobile');
        } else {
            document.body.classList.remove('mobile');
        }
    }

    // 全局鍵盤快捷鍵
    function handleGlobalKeydown(e) {
        // Ctrl/Cmd + K 聚焦到輸入框
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            chatInput.focus();
        }

        // Escape 關閉上傳區域
        if (e.key === 'Escape') {
            if (uploadSection.style.display !== 'none') {
                uploadSection.style.display = 'none';
            }
        }
    }

    // 添加全局事件監聽器
    window.addEventListener('resize', handleResize);
    document.addEventListener('keydown', handleGlobalKeydown);

    // 初始化響應式處理
    handleResize();
    
    // API 狀態檢查現在由狀態指示器自動處理，不需要額外調用

    // 添加示例提示
    function addExamplePrompts() {
        if (uploadedSources.length === 0) {
            const examples = [
                "上傳文件後，您可以問：",
                "• 這份文件的主要內容是什麼？",
                "• 請總結文件中的重點",
                "• 文件中提到了哪些關鍵概念？"
            ];

            setTimeout(() => {
                if (conversationHistory.length === 0 && uploadedSources.length === 0) {
                    const exampleDiv = document.createElement('div');
                    exampleDiv.className = 'example-prompts';
                    exampleDiv.innerHTML = `
                        <div style="text-align: center; padding: 20px; color: #5f6368; font-size: 14px;">
                            ${examples.map(example => `<p>${example}</p>`).join('')}
                        </div>
                    `;

                    const welcomeMessage = chatMessages.querySelector('.welcome-message');
                    if (welcomeMessage) {
                        welcomeMessage.appendChild(exampleDiv);
                    }
                }
            }, 3000);
        }
    }

    // 連線設置彈窗管理
    function initializeConnectionModal() {
        const modal = document.getElementById('connection-modal');
        const openBtn = document.getElementById('connection-settings-btn');
        const closeBtn = document.getElementById('close-connection-modal');
        const saveBtn = document.getElementById('save-settings-btn');
        const resetBtn = document.getElementById('reset-settings-btn');

        const ragUrlInput = document.getElementById('rag-api-url');
        const fileUrlInput = document.getElementById('file-api-url');
        const ragStatusEl = document.getElementById('rag-status');
        const fileStatusEl = document.getElementById('file-status');
        const testRagBtn = document.getElementById('test-rag-btn');
        const testFileBtn = document.getElementById('test-file-btn');

        // 打開彈窗
        openBtn.addEventListener('click', () => {
            modal.classList.add('show');
            loadCurrentSettings();
        });

        // 關閉彈窗
        closeBtn.addEventListener('click', () => {
            modal.classList.remove('show');
        });

        // 點擊背景關閉
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.classList.remove('show');
            }
        });

        // 載入當前設置
        function loadCurrentSettings() {
            ragUrlInput.value = connectionManager.settings.ragApiUrl;
            fileUrlInput.value = connectionManager.settings.fileApiUrl;

            // 重置狀態
            ragStatusEl.textContent = '未測試';
            ragStatusEl.className = 'status-indicator';
            fileStatusEl.textContent = '未測試';
            fileStatusEl.className = 'status-indicator';
        }

        // 測試連線
        testRagBtn.addEventListener('click', async () => {
            const url = ragUrlInput.value.trim();
            if (!url) return;

            ragStatusEl.textContent = '測試中...';
            ragStatusEl.className = 'status-indicator testing';
            testRagBtn.disabled = true;

            const result = await connectionManager.testConnection(url, 'rag');

            ragStatusEl.textContent = result.message;
            ragStatusEl.className = `status-indicator ${result.success ? 'connected' : 'disconnected'}`;
            testRagBtn.disabled = false;
        });

        testFileBtn.addEventListener('click', async () => {
            const url = fileUrlInput.value.trim();
            if (!url) return;

            fileStatusEl.textContent = '測試中...';
            fileStatusEl.className = 'status-indicator testing';
            testFileBtn.disabled = true;

            const result = await connectionManager.testConnection(url, 'file');

            fileStatusEl.textContent = result.message;
            fileStatusEl.className = `status-indicator ${result.success ? 'connected' : 'disconnected'}`;
            testFileBtn.disabled = false;
        });

        // 預設按鈕
        document.querySelectorAll('.preset-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const preset = btn.dataset.preset;
                const presets = connectionManager.getPresets();

                if (presets[preset]) {
                    ragUrlInput.value = presets[preset].ragApiUrl;
                    fileUrlInput.value = presets[preset].fileApiUrl;
                }

                // 更新按鈕狀態
                document.querySelectorAll('.preset-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
            });
        });

        // 保存設置
        saveBtn.addEventListener('click', () => {
            const ragUrl = ragUrlInput.value.trim();
            const fileUrl = fileUrlInput.value.trim();

            if (!ragUrl) {
                alert('請輸入 RAG API URL');
                return;
            }

            connectionManager.settings.ragApiUrl = ragUrl;
            connectionManager.settings.fileApiUrl = fileUrl;
            connectionManager.saveSettings();

            // 更新全局變數
            FILE_API_BASE = window.FILE_API_BASE;
            QUERY_API_BASE = window.QUERY_API_BASE;
            API_ENDPOINT = window.API_ENDPOINT;

            modal.classList.remove('show');

            // 顯示成功消息
            showNotification('連線設置已保存', 'success');

            // 重新檢查 API 狀態
            updateApiStatus();
        });

        // 重置設置
        resetBtn.addEventListener('click', () => {
            if (confirm('確定要重置為預設設置嗎？')) {
                localStorage.removeItem('rag-connection-settings');
                connectionManager.settings = connectionManager.loadSettings();
                connectionManager.initializeEndpoints();
                loadCurrentSettings();
                showNotification('設置已重置', 'info');
            }
        });
    }

    // 顯示通知
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            z-index: 3000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            animation: slideIn 0.3s ease;
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // RAG 架構圖功能
    function initializeRagDiagramModal() {
        const modal = document.getElementById('rag-diagram-modal');
        const openBtn = document.getElementById('rag-diagram-btn');
        const closeBtn = document.getElementById('rag-diagram-close');
        const applyBtn = document.getElementById('apply-diagram-params-btn');
        const resetBtn = document.getElementById('reset-diagram-params-btn');

        if (!modal || !openBtn) return;

        // 打開彈窗
        openBtn.addEventListener('click', () => {
            loadCurrentParamsToRagDiagram();
            modal.classList.add('show');
            // 透明度已固定為 0，不需要初始化
        });

        // 關閉彈窗
        closeBtn.addEventListener('click', () => {
            modal.classList.remove('show');
        });

        // 點擊背景關閉
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.classList.remove('show');
            }
        });

        // 應用參數
        applyBtn.addEventListener('click', applyRagDiagramParams);

        // 重置參數
        resetBtn.addEventListener('click', resetRagDiagramParams);

        // 透明度控制已移除，固定為完全透明
    }

    // 載入當前參數到架構圖
    function loadCurrentParamsToRagDiagram() {
        // 檢索參數
        document.getElementById('diagram-vector-top-k').value =
            document.getElementById('vector-top-k').value || 10;
        document.getElementById('diagram-bm25-top-k').value =
            document.getElementById('bm25-top-k').value || 10;
        document.getElementById('diagram-rerank-top-k').value =
            document.getElementById('rerank-top-k').value || 5;
        document.getElementById('diagram-rrf-k').value =
            document.getElementById('rrf-k').value || 60;
        document.getElementById('diagram-rrf-top-k').value =
            document.getElementById('rrf-top-k').value || 15;

        // LLM 參數
        document.getElementById('diagram-llm-temperature').value =
            document.getElementById('llm-temperature').value || 0.0;
        document.getElementById('diagram-llm-max-tokens').value =
            document.getElementById('llm-max-tokens').value || 3000;

        // 模型選擇
        document.getElementById('diagram-rerank-model').value =
            document.getElementById('rerank-model-type').value || 'onnx_int8';
        document.getElementById('diagram-llm-model').value =
            document.getElementById('llm-model-type').value || 'gemini-2.0-flash';

        // 系統設置
        document.getElementById('diagram-enable-rerank').checked =
            document.getElementById('enable-rerank').checked;
        document.getElementById('diagram-enable-bm25').checked =
            document.getElementById('enable-bm25').checked;
    }

    // 應用架構圖參數
    async function applyRagDiagramParams() {
        const statusEl = document.getElementById('diagram-status');
        const applyBtn = document.getElementById('apply-diagram-params-btn');

        try {
            applyBtn.disabled = true;
            statusEl.textContent = '正在應用參數...';
            statusEl.className = 'status-message';
            statusEl.style.display = 'block';

            // 同步參數到主面板
            document.getElementById('vector-top-k').value =
                document.getElementById('diagram-vector-top-k').value;
            document.getElementById('bm25-top-k').value =
                document.getElementById('diagram-bm25-top-k').value;
            document.getElementById('rerank-top-k').value =
                document.getElementById('diagram-rerank-top-k').value;
            document.getElementById('rrf-k').value =
                document.getElementById('diagram-rrf-k').value;
            document.getElementById('rrf-top-k').value =
                document.getElementById('diagram-rrf-top-k').value;
            document.getElementById('llm-temperature').value =
                document.getElementById('diagram-llm-temperature').value;
            document.getElementById('llm-max-tokens').value =
                document.getElementById('diagram-llm-max-tokens').value;
            document.getElementById('rerank-model-type').value =
                document.getElementById('diagram-rerank-model').value;
            document.getElementById('llm-model-type').value =
                document.getElementById('diagram-llm-model').value;
            document.getElementById('enable-rerank').checked =
                document.getElementById('diagram-enable-rerank').checked;
            document.getElementById('enable-bm25').checked =
                document.getElementById('diagram-enable-bm25').checked;

            // 應用配置
            const config = ragConfigManager.getCurrentConfig();
            await ragConfigManager.updateConfig(config);

            statusEl.textContent = '✅ 參數已成功應用！';
            statusEl.className = 'status-message success';

            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 3000);

        } catch (error) {
            console.error('應用參數失敗:', error);
            statusEl.textContent = '❌ 應用參數失敗: ' + error.message;
            statusEl.className = 'status-message error';
        } finally {
            applyBtn.disabled = false;
        }
    }

    // 重置架構圖參數
    async function resetRagDiagramParams() {
        if (!confirm('確定要重置為默認參數嗎？')) return;

        const statusEl = document.getElementById('diagram-status');
        const resetBtn = document.getElementById('reset-diagram-params-btn');

        try {
            resetBtn.disabled = true;
            statusEl.textContent = '正在重置參數...';
            statusEl.className = 'status-message';
            statusEl.style.display = 'block';

            // 重置為默認值
            document.getElementById('diagram-vector-top-k').value = 10;
            document.getElementById('diagram-bm25-top-k').value = 10;
            document.getElementById('diagram-rerank-top-k').value = 5;
            document.getElementById('diagram-rrf-k').value = 60;
            document.getElementById('diagram-rrf-top-k').value = 15;
            document.getElementById('diagram-llm-temperature').value = 0.0;
            document.getElementById('diagram-llm-max-tokens').value = 3000;
            document.getElementById('diagram-rerank-model').value = 'onnx_int8';
            document.getElementById('diagram-llm-model').value = 'gemini-2.0-flash';
            document.getElementById('diagram-enable-rerank').checked = true;
            document.getElementById('diagram-enable-bm25').checked = true;

            // 應用重置的參數
            await applyRagDiagramParams();

            statusEl.textContent = '🔄 參數已重置為默認值！';
            statusEl.className = 'status-message success';

        } catch (error) {
            console.error('重置參數失敗:', error);
            statusEl.textContent = '❌ 重置參數失敗: ' + error.message;
            statusEl.className = 'status-message error';
        } finally {
            resetBtn.disabled = false;
        }
    }

    // 更新參數框透明度
    // 透明度相關函數已移除，參數框固定為完全透明

    // 添加示例提示
    addExamplePrompts();

    // 初始化連線設置
    initializeConnectionModal();

    // 初始化 RAG 架構圖
    initializeRagDiagramModal();
});
