"""
embedding_generator.py

此模組提供了文本向量化（生成嵌入）的功能。
"""

from typing import List
import os
from sentence_transformers import SentenceTransformer # 導入 SentenceTransformer
import numpy as np # 導入 numpy

from dotenv import load_dotenv
import os
load_dotenv(os.path.join(os.path.dirname(__file__), '..', '.env')) # 加載 .env 檔案中的環境變數

LOCAL_EMBEDDING_MODEL_PATH = os.getenv("LOCAL_EMBEDDING_MODEL_PATH", "")
LOCAL_EMBEDDING_MODEL_DIMENSION = int(os.getenv("LOCAL_EMBEDDING_MODEL_DIMENSION", "768"))

class EmbeddingGenerator:
    """
    文本向量生成器，使用本地 Sentence-BERT 模型。
    """
    def __init__(self):
        """
        初始化 EmbeddingGenerator，加載本地模型。
        """
        if not os.path.exists(LOCAL_EMBEDDING_MODEL_PATH):
            raise ValueError(f"本地嵌入模型路徑 '{LOCAL_EMBEDDING_MODEL_PATH}' 不存在。請檢查路徑或下載模型。")
        
        try:
            self.model = SentenceTransformer(LOCAL_EMBEDDING_MODEL_PATH, trust_remote_code=True)
            # 驗證模型輸出維度是否與設定一致
            if self.model.get_sentence_embedding_dimension() != LOCAL_EMBEDDING_MODEL_DIMENSION:
                raise ValueError(f"模型 '{LOCAL_EMBEDDING_MODEL_PATH}' 的輸出維度 ({self.model.get_sentence_embedding_dimension()}) "
                                 f"與設定的維度 ({LOCAL_EMBEDDING_MODEL_DIMENSION}) 不匹配。")
            print(f"本地嵌入模型 '{LOCAL_EMBEDDING_MODEL_PATH}' 已成功加載。")
        except Exception as e:
            raise RuntimeError(f"加載本地嵌入模型失敗: {e}")

    def generate_embedding(self, text: str) -> List[float]:
        """
        為給定的文本生成向量嵌入。

        Args:
            text (str): 待生成嵌入的文本。

        Returns:
            List[float]: 文本的向量嵌入 (作為浮點數列表)。
        """
        try:
            # 使用本地模型生成嵌入，並轉換為 float32 numpy array
            embedding = self.model.encode(text, convert_to_numpy=True, normalize_embeddings=True).astype(np.float32)
            return embedding.tolist() # 返回 Python 列表
        except Exception as e:
            print(f"生成嵌入失敗: {e}")
            raise