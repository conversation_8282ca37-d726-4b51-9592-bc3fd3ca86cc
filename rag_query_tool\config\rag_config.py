"""
rag_config.py

RAG 系統參數配置管理
統一管理所有可調整的參數，支持動態更新
"""

from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from enum import Enum
import json
import os
from dotenv import load_dotenv

load_dotenv(os.path.join(os.path.dirname(__file__), '..', '.env'))

class RerankModelType(str, Enum):
    """Reranker 模型類型"""
    ONNX_FP32 = "onnx_fp32"
    ONNX_INT8 = "onnx_int8"

class LLMModelType(str, Enum):
    """LLM 模型類型"""
    GEMINI_2_0_FLASH = "gemini-2.0-flash"
    GEMINI_2_5_FLASH = "gemini-2.5-flash"

class RAGConfig(BaseModel):
    """RAG 系統配置參數"""
    
    # 檢索參數
    vector_top_k: int = Field(default=10, ge=1, le=50, description="向量檢索返回文檔數量")
    bm25_top_k: int = Field(default=10, ge=1, le=50, description="BM25 檢索返回文檔數量")
    rerank_top_k: int = Field(default=5, ge=1, le=20, description="重排序後返回文檔數量")
    
    # 相似度閾值
    similarity_threshold: float = Field(default=0.0, ge=0.0, le=1.0, description="向量相似度閾值")
    
    # LLM 生成參數
    llm_temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="LLM 生成溫度")
    llm_max_tokens: int = Field(default=1000, ge=100, le=4000, description="LLM 最大生成 token 數")
    llm_top_p: float = Field(default=0.9, ge=0.0, le=1.0, description="LLM Top-p 參數")
    
    # 上下文管理
    max_context_length: int = Field(default=3000, ge=500, le=8000, description="最大上下文長度")
    context_overlap: int = Field(default=200, ge=0, le=500, description="上下文重疊長度")
    
    # RRF 融合參數
    rrf_k: int = Field(default=60, ge=1, le=100, description="RRF 融合參數 k")
    rrf_top_k: int = Field(default=15, ge=5, le=30, description="RRF 融合後傳給 Reranker 的文檔數量")
    
    # 模型選擇
    rerank_model_type: RerankModelType = Field(default=RerankModelType.ONNX_INT8, description="重排序模型類型")
    llm_model_type: LLMModelType = Field(default=LLMModelType.GEMINI_2_0_FLASH, description="LLM 模型類型")

    # 系統參數
    enable_rerank: bool = Field(default=True, description="是否啟用重排序")
    enable_bm25: bool = Field(default=True, description="是否啟用 BM25 檢索")
    debug_mode: bool = Field(default=False, description="是否啟用調試模式")

    def get_rerank_onnx_path(self) -> str:
        """根據模型類型獲取 ONNX 模型路徑"""
        base_path = "rag_query_tool/embeddings/BAAI--bge-reranker-base/onnx_model"
        if self.rerank_model_type == RerankModelType.ONNX_FP32:
            return f"{base_path}/model.onnx"
        else:  # ONNX_INT8
            return f"{base_path}/model.int8.onnx"

    def get_llm_model_name(self) -> str:
        """獲取 LLM 模型名稱"""
        return self.llm_model_type.value

class RAGConfigManager:
    """RAG 配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or os.path.join(os.path.dirname(__file__), "rag_config.json")
        self._config = self._load_config()
    
    def _load_config(self) -> RAGConfig:
        """從文件加載配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                return RAGConfig(**config_data)
            except Exception as e:
                print(f"加載配置文件失敗: {e}，使用默認配置")
        
        # 使用默認配置
        return RAGConfig()
    
    def save_config(self) -> bool:
        """保存配置到文件"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config.model_dump(), f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失敗: {e}")
            return False
    
    def get_config(self) -> RAGConfig:
        """獲取當前配置"""
        return self._config
    
    def update_config(self, **kwargs) -> bool:
        """更新配置參數"""
        try:
            # 創建新的配置對象來驗證參數
            current_dict = self._config.model_dump()
            current_dict.update(kwargs)
            new_config = RAGConfig(**current_dict)
            
            # 驗證成功，更新配置
            self._config = new_config
            return self.save_config()
        except Exception as e:
            print(f"更新配置失敗: {e}")
            return False
    
    def reset_to_default(self) -> bool:
        """重置為默認配置"""
        self._config = RAGConfig()
        return self.save_config()
    
    def get_parameter(self, param_name: str) -> Any:
        """獲取單個參數值"""
        return getattr(self._config, param_name, None)
    
    def set_parameter(self, param_name: str, value: Any) -> bool:
        """設置單個參數值"""
        if hasattr(self._config, param_name):
            return self.update_config(**{param_name: value})
        return False
    
    def get_all_parameters(self) -> Dict[str, Any]:
        """獲取所有參數"""
        return self._config.model_dump()
    
    def get_parameter_info(self) -> Dict[str, Dict[str, Any]]:
        """獲取參數信息（包括描述、範圍等）"""
        schema = RAGConfig.model_json_schema()
        properties = schema.get('properties', {})
        
        param_info = {}
        for param_name, param_schema in properties.items():
            param_info[param_name] = {
                'description': param_schema.get('description', ''),
                'type': param_schema.get('type', 'unknown'),
                'default': param_schema.get('default'),
                'minimum': param_schema.get('minimum'),
                'maximum': param_schema.get('maximum'),
                'current_value': getattr(self._config, param_name)
            }
        
        return param_info

# 全局配置管理器實例
config_manager = RAGConfigManager()

def get_config() -> RAGConfig:
    """獲取全局配置"""
    return config_manager.get_config()

def update_config(**kwargs) -> bool:
    """更新全局配置"""
    return config_manager.update_config(**kwargs)

def get_parameter(param_name: str) -> Any:
    """獲取參數值"""
    return config_manager.get_parameter(param_name)

def set_parameter(param_name: str, value: Any) -> bool:
    """設置參數值"""
    return config_manager.set_parameter(param_name, value)
