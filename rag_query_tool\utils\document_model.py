"""
document_model.py

此模組定義了 Document 數據模型，用於表示文本塊及其相關元數據。
"""

from typing import Dict, Any, List

class Document:
    """
    表示一個文本塊及其相關元數據的數據模型。
    """
    def __init__(self, page_content: str, metadata: Dict[str, Any]):
        """
        初始化 Document 物件。

        Args:
            page_content (str): 文本塊的內容。
            metadata (Dict[str, Any]): 包含文本塊相關資訊的元數據字典。
                                        例如：{'source': 'file.md', 'page': 1, 'type': 'markdown_header'}
        """
        self.page_content = page_content
        self.metadata = metadata

    def to_dict(self) -> Dict[str, Any]:
        """
        將 Document 物件轉換為字典格式。

        Returns:
            Dict[str, Any]: 包含 'page_content' 和 'metadata' 的字典。
        """
        return {"page_content": self.page_content, "metadata": self.metadata}

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Document':
        """
        從字典格式創建 Document 物件。

        Args:
            data (Dict[str, Any]): 包含 'page_content' 和 'metadata' 的字典。

        Returns:
            Document: 新創建的 Document 物件。
        """
        return cls(data['page_content'], data['metadata'])

    def __repr__(self) -> str:
        """
        返回 Document 物件的字串表示形式。
        """
        return f"Document(page_content='{self.page_content[:50]}...', metadata={self.metadata})"

    def __str__(self) -> str:
        """
        返回 Document 物件的字串表示形式，主要顯示內容。
        """
        return self.page_content