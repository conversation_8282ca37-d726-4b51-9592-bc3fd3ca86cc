# Google Cloud Build 配置文件 - FastAPI Web Interface
steps:
  # 構建 Docker 映像
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '-t', 'gcr.io/$PROJECT_ID/rag-web-interface:$COMMIT_SHA',
      '-t', 'gcr.io/$PROJECT_ID/rag-web-interface:latest',
      '.'
    ]

  # 推送映像到 Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/rag-web-interface:$COMMIT_SHA']

  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/rag-web-interface:latest']

  # 部署到 Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args: [
      'run', 'deploy', 'rag-web-interface',
      '--image', 'gcr.io/$PROJECT_ID/rag-web-interface:$COMMIT_SHA',
      '--region', 'asia-east1',
      '--platform', 'managed',
      '--allow-unauthenticated',
      '--port', '8080',
      '--memory', '1Gi',
      '--cpu', '1',
      '--max-instances', '10',
      '--min-instances', '0',
      '--set-env-vars', 'PORT=8080,ENVIRONMENT=production,API_BASE_URL=${_API_BASE_URL},API_KEY=${_API_KEY},ALLOWED_HOSTS=${_ALLOWED_HOSTS}'
    ]

# 替換變數（在 Cloud Build 觸發器中設置）
substitutions:
  _API_BASE_URL: 'http://your-vm-external-ip'  # ⚠️ 替換為您的 VM 外部 IP
  _API_KEY: 'your-api-key-here'  # ⚠️ 替換為您的 API 金鑰
  _ALLOWED_HOSTS: '*.a.run.app'  # 允許的主機名

# 替換變數（在 Cloud Build 觸發器中設置）
substitutions:
  _API_BASE_URL: 'http://your-vm-external-ip'  # ⚠️ 替換為您的 VM 外部 IP
  _API_KEY: 'your-api-key-here'  # ⚠️ 替換為您的 API 金鑰
  _ALLOWED_HOSTS: '*.a.run.app'  # 允許的主機名

options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'
  machineType: 'E2_HIGHCPU_8'
