<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端連接診斷</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .loading { opacity: 0.6; }
    </style>
</head>
<body>
    <h1>🔍 前端連接診斷工具</h1>
    
    <div class="test-section">
        <h2>1. 基本環境檢測</h2>
        <button onclick="testEnvironment()">檢測環境</button>
        <div id="env-result"></div>
    </div>

    <div class="test-section">
        <h2>2. API 端點測試</h2>
        <button onclick="testHealthEndpoint()">測試健康檢查</button>
        <button onclick="testVMHealthEndpoint()">測試 VM 健康檢查</button>
        <button onclick="testConfigEndpoint()">測試配置端點</button>
        <div id="api-result"></div>
    </div>

    <div class="test-section">
        <h2>3. 查詢功能測試</h2>
        <button onclick="testQuery()">測試簡單查詢</button>
        <div id="query-result"></div>
    </div>

    <div class="test-section">
        <h2>4. 連接設置測試</h2>
        <button onclick="testConnectionSettings()">檢查連接設置</button>
        <div id="connection-result"></div>
    </div>

    <div class="test-section">
        <h2>5. 直連 VM 測試</h2>
        <button onclick="testDirectVMConnection()">測試直連 VM</button>
        <button onclick="testMixedContentPolicy()">檢查 Mixed Content 政策</button>
        <div id="vm-direct-result"></div>
    </div>

    <div class="test-section">
        <h2>6. 瀏覽器環境檢測</h2>
        <button onclick="testBrowserEnvironment()">檢測瀏覽器環境</button>
        <div id="browser-result"></div>
    </div>

    <script>
        function showResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            container.appendChild(div);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        async function testEnvironment() {
            clearResults('env-result');
            
            const hostname = window.location.hostname;
            const origin = window.location.origin;
            const isProduction = hostname !== 'localhost' && !hostname.startsWith('127.0.0.1');
            
            showResult('env-result', `主機名: ${hostname}`, 'info');
            showResult('env-result', `來源: ${origin}`, 'info');
            showResult('env-result', `生產環境: ${isProduction ? '是' : '否'}`, 'info');
            
            // 檢測預期的 API URL
            const expectedApiUrl = isProduction ? `${origin}/api` : 'http://localhost:8001';
            showResult('env-result', `預期 API URL: ${expectedApiUrl}`, 'info');
        }

        async function testHealthEndpoint() {
            clearResults('api-result');
            showResult('api-result', '測試 Web 界面健康檢查...', 'info');
            
            try {
                const response = await fetch('/health');
                const data = await response.json();
                
                if (response.ok) {
                    showResult('api-result', `✅ Web 界面健康: ${JSON.stringify(data)}`, 'success');
                } else {
                    showResult('api-result', `❌ Web 界面健康檢查失敗: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('api-result', `❌ Web 界面健康檢查錯誤: ${error.message}`, 'error');
            }
        }

        async function testVMHealthEndpoint() {
            showResult('api-result', '測試 VM 健康檢查代理...', 'info');
            
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                if (response.ok) {
                    showResult('api-result', `✅ VM 健康檢查: ${JSON.stringify(data)}`, 'success');
                } else {
                    showResult('api-result', `❌ VM 健康檢查失敗: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('api-result', `❌ VM 健康檢查錯誤: ${error.message}`, 'error');
            }
        }

        async function testConfigEndpoint() {
            showResult('api-result', '測試配置端點...', 'info');
            
            try {
                const response = await fetch('/api/config');
                const data = await response.json();
                
                if (response.ok) {
                    showResult('api-result', `✅ 配置端點: 獲取到 ${Object.keys(data).length} 個配置項`, 'success');
                    showResult('api-result', `配置詳情: ${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    showResult('api-result', `❌ 配置端點失敗: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('api-result', `❌ 配置端點錯誤: ${error.message}`, 'error');
            }
        }

        async function testQuery() {
            clearResults('query-result');
            showResult('query-result', '測試查詢功能...', 'info');
            
            const testQuery = {
                query_text: "測試查詢",
                include_llm_response: false,
                raw_documents_only: true
            };
            
            try {
                const response = await fetch('/api/query', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testQuery)
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult('query-result', `✅ 查詢成功: 返回 ${data.documents ? data.documents.length : 0} 個文檔`, 'success');
                    if (data.processing_info) {
                        showResult('query-result', `處理時間: ${data.processing_info.total_time}秒`, 'info');
                    }
                } else {
                    showResult('query-result', `❌ 查詢失敗: ${response.status} - ${JSON.stringify(data)}`, 'error');
                }
            } catch (error) {
                showResult('query-result', `❌ 查詢錯誤: ${error.message}`, 'error');
            }
        }

        async function testConnectionSettings() {
            clearResults('connection-result');
            
            // 檢查 localStorage
            const saved = localStorage.getItem('rag-connection-settings');
            if (saved) {
                try {
                    const settings = JSON.parse(saved);
                    showResult('connection-result', `✅ 找到保存的設置: ${JSON.stringify(settings)}`, 'success');
                } catch (error) {
                    showResult('connection-result', `❌ 設置解析錯誤: ${error.message}`, 'error');
                }
            } else {
                showResult('connection-result', `ℹ️ 沒有保存的連接設置`, 'info');
            }
            
            // 檢查全局變量
            if (window.QUERY_API_BASE) {
                showResult('connection-result', `✅ 全局 API 端點: ${window.QUERY_API_BASE}`, 'success');
            } else {
                showResult('connection-result', `❌ 全局 API 端點未設置`, 'error');
            }
        }

        // 頁面載入時自動執行基本檢測
        window.addEventListener('load', () => {
            testEnvironment();
            setTimeout(testHealthEndpoint, 1000);
            setTimeout(testVMHealthEndpoint, 2000);
        });
    </script>
</body>
</html>
