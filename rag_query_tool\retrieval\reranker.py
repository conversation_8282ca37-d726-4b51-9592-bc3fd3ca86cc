"""
reranker.py

此模組提供基於預訓練模型 (例如 BGE-reranker) 的重排序功能。
它能夠對初步檢索到的文檔進行重新評分，以提升相關性排序。
"""

from typing import List, Dict, Any
import os
from transformers import AutoTokenizer, AutoModelForSequenceClassification
import torch
import onnxruntime as ort # 導入 onnxruntime
from dotenv import load_dotenv

# 載入環境變數
load_dotenv(os.path.join(os.path.dirname(__file__), '..', '.env'))

class Reranker:
    """
    Reranker 類別，用於載入和執行重排序模型。
    """
    def __init__(self, model_name_or_path: str = None, onnx_model_path: str = None):
        """
        初始化 Reranker。

        Args:
            model_name_or_path (str): 重排序模型的名稱或本地路徑。如果為 None，從環境變數讀取。
            onnx_model_path (str): ONNX 模型的保存路徑。如果為 None，從環境變數讀取。
        """
        # 從環境變數讀取路徑（如果參數為 None）
        if model_name_or_path is None:
            model_name_or_path = os.getenv("RERANK_PATH", "BAAI/bge-reranker-base")
        if onnx_model_path is None:
            onnx_model_path = os.getenv("RERANK_ONNX_PATH", "rag_query_tool/embeddings/BAAI--bge-reranker-base/onnx_model/model.int8.onnx")

        self.model_name_or_path = model_name_or_path
        # 嘗試多個可能的 tokenizer 路徑
        tokenizer_paths = [
            os.path.dirname(onnx_model_path),  # onnx_model 目錄
            model_name_or_path,  # 原始模型路徑
            "BAAI/bge-reranker-base"  # Hugging Face 模型名稱作為後備
        ]

        self.tokenizer = None
        for tokenizer_path in tokenizer_paths:
            try:
                self.tokenizer = AutoTokenizer.from_pretrained(tokenizer_path, use_fast=False)
                print(f"成功載入 tokenizer 從: {tokenizer_path}")
                break
            except Exception as e:
                print(f"無法從 {tokenizer_path} 載入 tokenizer: {e}")
                continue

        if self.tokenizer is None:
            raise RuntimeError("無法載入 tokenizer，請檢查模型路徑或網路連接")
        self.onnx_model_path = onnx_model_path
        self.session = None

        self._load_model()

    def _load_model(self):
        """載入 ONNX 模型"""
        if os.path.exists(self.onnx_model_path):
            # 如果 ONNX 模型已存在，直接載入
            # 創建 session 選項以優化 int8 量化模型
            sess_options = ort.SessionOptions()
            sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL

            self.session = ort.InferenceSession(
                self.onnx_model_path,
                sess_options=sess_options,
                providers=["CPUExecutionProvider"]
            )

            print(f"ONNX 重排序模型 '{self.onnx_model_path}' 已成功載入。")
            print(f"使用 CPU 執行提供者")
            print(f"模型輸入: {[input.name for input in self.session.get_inputs()]}")
            print(f"模型輸出: {[output.name for output in self.session.get_outputs()]}")
        else:
            # 如果 ONNX 模型不存在，載入 PyTorch 模型並轉換
            print(f"ONNX 模型 '{self.onnx_model_path}' 不存在，正在載入 PyTorch 模型並轉換...")
            model = AutoModelForSequenceClassification.from_pretrained(self.model_name_or_path)
            model.eval() # 設定為評估模式

            # 創建一個虛擬輸入，用於 ONNX 導出
            dummy_input = self.tokenizer(["這是查詢", "這是文檔內容"], padding=True, truncation=True, return_tensors='pt', max_length=512)
            
            # 導出為 ONNX
            # 根據 dummy_input 中實際存在的鍵來構建輸入
            input_args = tuple(dummy_input[k] for k in dummy_input.keys())
            input_names = list(dummy_input.keys())
            dynamic_axes = {k: {0: 'batch_size', 1: 'sequence_length'} for k in dummy_input.keys()}
            dynamic_axes['logits'] = {0: 'batch_size'}

            torch.onnx.export(
                model,
                input_args,
                self.onnx_model_path,
                input_names=input_names,
                output_names=['logits'],
                dynamic_axes=dynamic_axes,
                opset_version=10 # 根據 PyTorch 和 ONNX Runtime 版本選擇合適的 opset
            )
            print(f"PyTorch 模型已成功轉換並保存為 ONNX 格式: '{self.onnx_model_path}'。")
            self.session = ort.InferenceSession(self.onnx_model_path)

    def rerank(self, query: str, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        對文檔進行重排序。

        Args:
            query (str): 查詢文本。
            documents (List[Dict[str, Any]]): 待重排序的文檔列表，每個字典應包含 'doc' (ChildDocument 物件)。

        Returns:
            List[Dict[str, Any]]: 經過重排序的文檔列表，包含原始文檔物件和新的重排序分數。
        """
        if not documents:
            return []

        pairs = [[query, doc['doc'].page_content] for doc in documents]
        
        inputs = self.tokenizer(
            pairs,
            padding=True,
            truncation=True,
            return_tensors='np',
            max_length=512,
            return_token_type_ids=True  # 強制生成 token_type_ids
        )

        # ONNX Runtime 推理
        ort_inputs = {
            "input_ids": inputs["input_ids"],
            "attention_mask": inputs["attention_mask"],
        }

        # 添加 token_type_ids（如果存在）
        if "token_type_ids" in inputs:
            ort_inputs["token_type_ids"] = inputs["token_type_ids"]
        else:
            # 如果 tokenizer 沒有生成 token_type_ids，手動創建
            import numpy as np
            batch_size, seq_len = inputs["input_ids"].shape
            ort_inputs["token_type_ids"] = np.zeros((batch_size, seq_len), dtype=np.int64)

        ort_outputs = self.session.run(None, ort_inputs)
        scores = ort_outputs[0].squeeze(axis=1) # ONNX Runtime 的輸出是 numpy array

        # 將分數與原始文檔配對
        reranked_results = []
        for i, doc in enumerate(documents):
            # 直接使用原始文檔對象，添加重排序分數
            original_doc = doc['doc']
            original_doc.retrieval_method = doc.get('method', 'N/A')  # 保留原始檢索方法
            original_doc.fused_score = doc.get('fused_score', 'N/A')  # 保留 RRF 分數
            original_doc.rerank_score = scores[i].item()  # 添加重排序分數
            reranked_results.append(original_doc)

        # 根據重排序分數降序排列
        reranked_results.sort(key=lambda x: x.rerank_score, reverse=True)
        return reranked_results

    def reload_model(self, new_onnx_path: str):
        """重新載入不同的 ONNX 模型"""
        if self.session:
            # 清理舊的 session
            del self.session
            self.session = None

        self.onnx_model_path = new_onnx_path
        self._load_model()
        print(f"已切換到新的重排序模型: {new_onnx_path}")

    def reload_model(self, new_onnx_path: str):
        """重新載入不同的 ONNX 模型"""
        if self.session:
            # 清理舊的 session
            del self.session
            self.session = None

        self.onnx_model_path = new_onnx_path
        self._load_model()
        print(f"已切換到新的重排序模型: {new_onnx_path}")
