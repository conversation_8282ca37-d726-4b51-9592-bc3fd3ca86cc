"""
llm_response_generator.py

此模組提供與大型語言模型 (LLM) 互動的功能，用於根據提供的上下文和查詢生成回答。
"""

import google.generativeai as genai
from typing import List, Dict, Any

from dotenv import load_dotenv
import os
load_dotenv(os.path.join(os.path.dirname(__file__), '..', '.env')) # 加載 .env 檔案中的環境變數

GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "") # 修改導入路徑

class LLMResponseGenerator:
    """
    LLMResponseGenerator 類別，用於根據上下文和查詢生成 LLM 回答。
    """
    def __init__(self, model_name: str = 'gemini-2.0-flash'):
        """
        初始化 LLMResponseGenerator，配置 Gemini API。

        Args:
            model_name (str): Gemini 模型名稱，默認為 'gemini-2.0-flash'
        """
        if not GEMINI_API_KEY:
            raise ValueError("GEMINI_API_KEY 未設定。請在 query_tool/config/settings.py 或環境變數中設定。")
        genai.configure(api_key=GEMINI_API_KEY)
        self.current_model_name = model_name
        self.model = genai.GenerativeModel(model_name)

    def generate_response(self, query: str, context_documents: List[Dict[str, Any]], temperature: float = 0.7, max_tokens: int = 1000, top_p: float = 0.9) -> str:
        """
        根據提供的上下文文檔和查詢生成 LLM 回答。

        Args:
            query (str): 使用者查詢。
            context_documents (List[Dict[str, Any]]): 相關的上下文文檔列表，每個字典應包含 'page_content'。
            temperature (float): 生成溫度，控制回答的創造性。
            max_tokens (int): 最大生成 token 數。
            top_p (float): Top-p 參數，控制詞彙選擇的多樣性。

        Returns:
            str: LLM 生成的回答。
        """
        context_str = "\n".join([doc['page_content'] for doc in context_documents])

        # 調整提示詞，使其在保持嚴謹的同時，更鼓勵利用上下文
        SYSTEM_PROMPT = """你是一個嚴謹的資料查詢助手，只能根據使用者提供的文件內容來回答問題。

如果文件內容包含問題的答案，請盡可能詳細地回答。
若文件中找不到直接的答案，請明確指出「無法從文件中找到答案」，不要加入自己的推測或知識。

文件內容如下：
----------------
{context_str}
----------------

請回答以下問題：
{query_str}

你的回答應該只根據上方的文件內容。
"""
        prompt = SYSTEM_PROMPT.format(context_str=context_str, query_str=query)

        try:
            # 配置生成參數
            generation_config = genai.types.GenerationConfig(
                temperature=temperature,
                max_output_tokens=max_tokens,
                top_p=top_p
            )

            response = self.model.generate_content(prompt, generation_config=generation_config)
            return response.text
        except Exception as e:
            print(f"生成 LLM 回答時發生錯誤: {e}")
            return "生成回答時發生錯誤。"

    def switch_model(self, new_model_name: str):
        """切換到不同的 LLM 模型"""
        if new_model_name != self.current_model_name:
            self.current_model_name = new_model_name
            self.model = genai.GenerativeModel(new_model_name)
            print(f"已切換到新的 LLM 模型: {new_model_name}")

    def get_current_model(self) -> str:
        """獲取當前使用的模型名稱"""
        return self.current_model_name
